import request from '@/utils/request'

// 获取所有方案
export function manageAllApi(obj) {
  return request({
    url: '/plan/manage/all',
    method: 'post',
    data: obj
  })
}


// 获取舆情预警列表
export function getWarnDataApi(obj) {
  return request({
    url: '/warn/data/get',
    method: 'post',
    data: obj
  })
}


// 舆情预警阅读
export function warnReadApi(obj) {
  return request({
    url: '/WarnRead/add',
    method: 'post',
    data: obj
  })
}


// 批量导出舆情预警
export function warnExportApi(obj) {
  return request({
    url: '/warn/data/export',
    method: 'post',
    data: obj
  })
}

// 批量导入至素材
export function warnMaterialAddApi(obj) {
  return request({
    url: '/warn/data/material/add',
    method: 'post',
    data: obj
  })
}


// 敏感信息占比
export function emotionAnalyse(data) {
  return request({
    url: `/warnAnalyse/emotionAnalyse`,
    method: 'post',
    data
  })
}
// 信息来源占比
export function mediaTypeAnalyse(data) {
  return request({
    url: `/warnAnalyse/mediaTypeAnalyse`,
    method: 'post',
    data
  })
}
// 敏感信息top10
export function emotionAnalyseTop(data) {
  return request({
    url: `/warnAnalyse/emotionAnalyseTop`,
    method: 'post',
    data
  })
}
// 关键词云
export function wordAnalyse(data) {
  return request({
    url: `/warnAnalyse/wordAnalyse`,
    method: 'post',
    data
  })
}
// 信息来源走势图
export function getType(data) {
  return request({
    url: '/warnAnalyse/time/type',
    method: 'post',
    data
  })
}
// 热点事件分布
export function getAreaMapApi(data) {
  return request({
    url: '/analyse/areaMap',
    method: 'post',
    data
  })
}

// 信息关注活跃度地图
export function infoFocusAreaMapApi(data) {
  return request({
    url: '/warnAnalyse/infoFocusAreaMap',
    method: 'post',
    data
  })
}