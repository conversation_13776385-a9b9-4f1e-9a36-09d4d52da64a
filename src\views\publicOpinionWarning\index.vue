<template>
  <div class="publicOpinionWarning">
    <div class="search-wrap">
      <div class="title"><span>筛选条件</span></div>
      <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">
        <el-form-item label="发送时间:" label-width="100px">
          <el-date-picker v-model="queryForm.warnDateStart" type="datetime" placeholder="开始日期" clearable
                          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                          :picker-options="pickerOptionsStart" @change="startChange">
          </el-date-picker>
          -
          <el-date-picker v-model="queryForm.warnDateEnd" type="datetime" placeholder="结束日期" clearable
                          format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss"
                          :picker-options="pickerOptionsEnd" @change="endChange">
          </el-date-picker>

          <!-- <el-date-picker style="width:175px" type="datetime" size="mini"  :picker-options="pickerOptionsStart" @change="startChange" v-model="startTime" :clearable="true" format="yyyy-MM-dd HH:mm:ss" value-format="yyyy-MM-dd HH:mm:ss" placeholder="请输入开始时间"></el-date-picker> -->

        </el-form-item>
        <el-form-item label="信息属性" prop="emotionFlag" label-width="100px">
          <el-select v-model="queryForm.emotionFlag" placeholder="请选择信息属性" clearable size="small">
            <el-option v-for="(item,index) in emotionData" :key="index" :label="item.name"
                       :value="item.value"/>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="信息属性:" label-width="100px">
            <el-select v-model="queryForm.selectedValues" collapse-tags multiple @remove-tag="removeTag"
                @change="handleSelectChange">
                <el-option label="全部" value="all" @click.native="selectAll"></el-option>
                <el-option v-for="(item,index) in emotionData" :key="index" :label="item.name"
                    :value="item.value"></el-option>
            </el-select>
        </el-form-item> -->


        <!-- <el-form-item label="信息排序:" label-width="100px">
            <el-select v-model="queryForm.sort">
                <el-option v-for="(item,index) in msgData" :key="index" :label="item.name"
                    :value="item.value"></el-option>
            </el-select>
        </el-form-item> -->

        <el-form-item label="所属方案" prop="planId" label-width="100px">
          <el-select v-model="queryForm.planId" placeholder="请选择方案" clearable size="small">
            <el-option v-for="item in caseData" :key="item.planId" :label="item.planName"
                       :value="item.planId"/>
          </el-select>
        </el-form-item>
        <el-form-item label="来源类型" prop="type" label-width="100px">
          <el-select v-model="queryForm.type" placeholder="请选择类型" clearable size="small" multiple
                     collapse-tags>
            <el-option v-for="dict in mediaList" :key="dict.dictValue" :label="dict.dictLabel"
                       :value="dict.dictValue"/>
          </el-select>
        </el-form-item>
        <el-form-item label="信息浏览:" label-width="100px">
          <el-select v-model="queryForm.readFlag" clearable>
            <el-option v-for="(item,index) in readList" :key="index" :label="item.name"
                       :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="预警类型:" label-width="100px">
            <el-select v-model="queryForm.warnType" clearable>
                <el-option v-for="(item,index) in warnTypeList" :key="index" :label="item.name"
                    :value="item.value"></el-option>
            </el-select>
        </el-form-item> -->
        <el-form-item label="推送方式" prop="pushType" label-width="100px">
          <el-select v-model="queryForm.pushType" placeholder="请选择类型" clearable size="small" multiple
            collapse-tags>
            <el-option v-for="(item,index) in pushTypeList" :key="index" :label="item.name"
                :value="item.value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="预警内容:" label-width="100px">
          <el-input v-model="queryForm.kw" size="small" clearable placeholder="请输入预警内容"/>
        </el-form-item>


      </el-form>
      <div class="search-btn">
        <el-button type="primary" @click="submitSearch('query')">查询</el-button>
      </div>
    </div>
    <div class="wrap-content">
      <div class="data-table" ref="data_table">
        <div :class="titleFixed?'dataTot is_data_fixed':'dataTot'">
          <div class="mt-4 dataSel">
                        <span v-show="total>0">
                            <el-select size="small" v-model="exportNum" placeholder="选择导出条数"
                                       style="width: 125px;margin-right: 10px;" clearable @change="exportNumChange">
                                <el-option label="选择当前页" value="0"/>
                                <el-option label="前500条" value="500"/>
                                <el-option label="前1000条" value="1000"/>
                                <el-option label="前5000条" value="5000"/>
                            </el-select>
                            <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
                                <div><el-button type="text" style="margin-right: 10px;">批量导入至素材</el-button></div>
                                <el-dropdown-menu slot="dropdown" class="subClass">
                                    <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                                                      @mouseenter="() => {$refs.Dropdown.show()}">
                                        <template v-if="!item.children">{{ item.folderName }}</template>
                                        <template v-else>
                                            <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                                                <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                                                <span class="el-dropdown-link" style="color: #606266;">
                                                    {{ item.folderName }}<i v-if="item.children.length != 0"
                                                                            class="el-icon-arrow-right el-icon--right"/>
                                                </span>
                                                <el-dropdown-menu slot="dropdown" class="menuClass">
                                                    <el-dropdown-item v-for="subItem in item.children" :key="subItem.id"
                                                                      :command="subItem.id"
                                                                      @click.native="importToMaterials(subItem)">
                                                        {{ subItem.folderName }}
                                                    </el-dropdown-item>
                                                </el-dropdown-menu>
                                            </el-dropdown>
                                        </template>
                                    </el-dropdown-item>
                                </el-dropdown-menu>
                            </el-dropdown>
                            <el-button :loading="downloadLoading" type="text"
                                       @click="exportExcel()">导出Excel</el-button>
                        </span>
            <span v-show="total<=0"></span>
            <el-button type="text" @click="goAnalysis">预警分析</el-button>
          </div>
        </div>
        <div class="dataTable">
          <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border style="width: 100%"
                    :header-cell-style="{background:'#fcfcfd'}" :class="titleFixed?'result-fiexd':''"
                    @selection-change="handleSelectionChange">
            <el-table-column type="selection" width="50" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="left" header-align="center">
              <template #default="scope">
                <div :class="scope.row.readFlag==1?'tableItemTitle cover-column':'tableItemTitle'">
                  <div class="tableTitle" @click="goDetail(scope.row)">
                    <img class="tableItemImg" :src="transImage(scope.row.type,scope.row.host||'')" alt="无图片"/>
                    <el-tooltip placement="top" effect="light" raw-content>
                      <div slot="content">
                        <div v-html="scope.row.title"></div>
                      </div>
                      <div class="tableTitleSpan">
                                                <span>{{(queryForm.pageNum - 1) * queryForm.pageSize + scope.$index +
                                                    1}}. </span>
                        <span v-html="scope.row.title"></span>
                      </div>
                    </el-tooltip>
                    <!-- <el-select v-model="scope.row.emotionFlag"
                        :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                        size="mini" placeholder="请选择"
                        @change="(val)=>{changeSensitive(val,scope.row)}">
                        <el-option :key="2" label="非敏感" :value="2"></el-option>
                        <el-option :key="1" label="敏感" :value="1"></el-option>
                        <el-option :key="0" label="中性" :value="0"></el-option>
                    </el-select> -->
                    <p class="article-type"
                       :style="scope.row.emotionFlag==2?'background-color: #a8dfef;color: #006e8f;':scope.row.emotionFlag==1?'background-color: #fcc3c2;color: #b52626;':'background-color: #ffdc70;color: #8e5d00;'"
                    >{{scope.row.emotionFlag==2?"非敏感":scope.row.emotionFlag==1?"敏感":"中性"}}</p>
                    <p class="article-type" style="background-color: #339593"
                       v-show="scope.row.isOriginal">原创</p>
                    <p class="article-type" v-show="!scope.row.isOriginal">转载</p>
                    <!-- <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;"
                        v-show="scope.row.warned==1">流转中</p> -->
                    <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;"
                       v-show="scope.row.deal==1">已处置</p>
                    <p class="article-type" style="background-color: #D8D8D8;color: #999999;"
                       v-if="scope.row.urlAccessStatus==0">已删除</p>
                    <p class="article-type" style="background-color: #D5F8D1;color: #3EC82F;"
                       v-else>可访问</p>
                  </div>
                  <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
                  <div class="tableFoot">
                    <div class="footInfo">
                      <img src="@/assets/images/message.png" alt="" class="footIcon"/>
                      <span>{{scope.row.commentNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <img src="@/assets/images/book.png" alt="" class="footIcon"/>
                      <span>{{scope.row.readNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <img src="@/assets/images/good.png" alt="" class="footIcon"/>
                      <span>{{scope.row.likeNum || 0}}</span>
                    </div>
                    <div class="footInfo">
                      <img src="@/assets/images/share.png" alt="" class="footIcon"/>
                      <span>{{scope.row.reprintNum || 0}}</span>
                    </div>
                    <div class="footButtonGroup">
                      <div>
                        <div class="footButonItem" v-show="scope.row.hitWords">
                          <el-tooltip effect="light" content="涉及词" placement="top">
                            <img src="@/assets/images/keyword.png" alt=""
                                 class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitWords"
                                      placement="top">
                            <span class="keyword">{{ scope.row.hitWords || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.hitCourtNames">
                          <el-tooltip effect="light" content="涉及法院" placement="top">
                            <img src="@/assets/images/court.png" alt="" class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.hitCourtNames"
                                      placement="top">
                                                        <span class="keyword" style="color: #247CFF;">{{
                                                            scope.row.hitCourtNames || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-show="scope.row.contentAreaCodeName">
                          <el-tooltip effect="light" content="精准地域" placement="top">
                            <img src="@/assets/images/areaDetail.png" alt=""
                                 class="footIcon"/>
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.contentAreaCodeName"
                                      placement="top">
                                                        <span class="keyword" style="color: #356391;">{{
                                                            scope.row.contentAreaCodeName || '' }}</span>
                          </el-tooltip>
                        </div>
                        <div class="footButonItem" v-if="scope.row.contentMeta&&scope.row.contentMeta.length">
                          <el-tooltip effect="light" content="信息分类" placement="top">
                            <img src="@/assets/images/contentMeta.png" alt="" class="footIcon" />
                          </el-tooltip>
                          <el-tooltip effect="light" :content="scope.row.contentMeta.join(' ')" placement="top">
                            <span class="keyword" style="color: #666;">{{ scope.row.contentMeta.join(' ') }}</span>
                          </el-tooltip>
                        </div>
                      </div>

                      <div style="white-space: nowrap;">
                        <div class="footButonItem" @click="copyAritical(scope.row)">
                          <img src="@/assets/images/copy.png" alt="" class="footIcon"/>
                          <span>复制</span>
                        </div>
                        <!-- <div class="footButonItem" @click="openSendMsg(scope.row)">
                            <img src="@/assets/images/send.png" alt="" class="footIcon" />
                            <span>报送</span>
                        </div> -->
                        <div class="footButonItem" @click="goOrigin(scope.row.url)">
                          <img src="@/assets/images/goOrigin.png" alt="" class="footIcon"/>
                          <span>查看原文</span>
                        </div>
                        <div class="footButonItem" @click="copyText(scope.row.url,true)">
                          <img src="@/assets/images/copyLink.png" alt="" class="footIcon"/>
                          <span>拷贝地址</span>
                        </div>
                        <!-- <el-dropdown>
                          <div  class="footButonItem">
                              <img src="@/assets/images/filterInfo.png" alt="" class="footIcon" />
                              <span>过滤信息</span>
                          </div>
                          <el-dropdown-menu  slot="dropdown">
                              <el-dropdown-item @click.native="filterOne(scope.row)">过滤单条</el-dropdown-item>
                              <el-dropdown-item @click.native="filterWeb(scope.row)">过滤站点</el-dropdown-item>
                          </el-dropdown-menu>
                      </el-dropdown> -->
                        <el-dropdown>
                          <div class="footButonItem">
                            <img src="@/assets/images/markIcon.png" alt=""
                                 class="footIcon"/>
                            <span>标记</span>
                          </div>
                          <el-dropdown-menu slot="dropdown">
                            <el-dropdown-item
                              @click.native="markDisposition(scope.row)">{{scope.row.deal==1?'取消处置':'处置'}}
                            </el-dropdown-item>
                            <el-dropdown-item
                              @click.native="markKeyFocus(scope.row)">{{scope.row.follow==1?'取消重点关注':'重点关注'}}
                            </el-dropdown-item>
                          </el-dropdown-menu>
                        </el-dropdown>
                      </div>
                    </div>
                  </div>
                </div>
                <img class="read-img" v-if="scope.row.readFlag==1" src="@/assets/images/read.png"
                     alt="">
                <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png"
                     alt="">
              </template>
            </el-table-column>
            <!-- <el-table-column prop="count" label="相似信息" align="center" width="100px">
            </el-table-column> -->
            <!-- <el-table-column prop="nickname" label="来源" align="center" width="100px">
                <template #default="scope">
                    <div v-show="scope.row.typeName!='短视频'">{{ scope.row.typeName }}</div>
                    <div>{{ scope.row.host }}</div>
                    <div v-show="scope.row.typeName=='短视频'">{{ scope.row.author }}</div>
                </template>
            </el-table-column> -->
            <!-- <el-table-column prop="warningType" label="预警类型" align="center" width="100px">
            </el-table-column> -->
            <el-table-column prop="warnGrade" label="预警等级" align="center" width="100px">
              <template slot-scope="scope">
                <div>{{ scope.row.warnGrade==1?'一般':scope.row.warnGrade==2?'中等':scope.row.warnGrade==3?'严重':''
                  }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="publishTime" align="center" width="150px">
              <template slot="header">
                预警时间
              </template>
              <template slot-scope="scope">
                <div>{{ scope.row.warnTime.substring(0,10) }}</div>
                <div>{{ scope.row.warnTime.substring(11,19) }}</div>
              </template>
            </el-table-column>
            <el-table-column prop="pushType" label="推送方式" align="center" width="150px">
              <template slot-scope="scope">
                <div>{{selectDictLabelArray(pushTypeList, scope.row.pushType)}}</div>
              </template>
            </el-table-column>
            <el-table-column prop="publishTime" align="center" width="150px">
              <template slot="header">
                原文时间
                <!-- <span class="sortIconGroup">
                    <i :class="`el-icon-caret-top ${(queryForm.sort && queryForm.sort)=='4'?'active':''}`"
                        @click="sortChange('4')"></i>
                    <i :class="`el-icon-caret-bottom ${(queryForm.sort && queryForm.sort)=='3'?'active':''}`"
                        @click="sortChange('3')"></i>
                </span> -->
              </template>
              <template slot-scope="scope">
                <div>{{ scope.row.publishTime.substring(0,10) }}</div>
                <div>{{ scope.row.publishTime.substring(11,19) }}</div>
              </template>
            </el-table-column>
          </el-table>
          <div v-show="total>0"
               style="width:100%;min-height: 20px;display:flex;align-items:center;justify-content: space-between;">
            <div style="margin-top:10px;">
              <el-select v-model="exportNum" size="small" placeholder="选择导出条数"
                         style="width: 125px;margin-right: 10px" clearable @change="exportNumChange">
                <el-option label="选择当前页" value="0"/>
                <el-option label="前500条" value="500"/>
                <el-option label="前1000条" value="1000"/>
                <el-option label="前5000条" value="5000"/>
              </el-select>
              <el-dropdown placement="bottom" ref="Dropdown" trigger="click">
                <div>
                  <el-button type="text" style="margin-right: 10px;">批量导入至素材</el-button>
                </div>
                <el-dropdown-menu slot="dropdown" class="subClass">
                  <el-dropdown-item v-for="item in treeDataauto" :key="item.id" :command="item.id"
                                    @mouseenter="() => {$refs.Dropdown.show()}">
                    <template v-if="!item.children">{{ item.folderName }}</template>
                    <template v-else>
                      <el-dropdown trigger="hover" placement="right-start" :show-timeout="1">
                        <!-- 手动控制hover显示，解决鼠标移入二级菜单时一级菜单消失问题 -->
                        <span class="el-dropdown-link" style="color: #606266;">
                                                    {{ item.folderName }}<i v-if="item.children.length != 0"
                                                                            class="el-icon-arrow-right el-icon--right"/>
                                                </span>
                        <el-dropdown-menu slot="dropdown" class="menuClass">
                          <el-dropdown-item v-for="subItem in item.children" :key="subItem.id"
                                            :command="subItem.id"
                                            @click.native="importToMaterials(subItem)">
                            {{ subItem.folderName }}
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </el-dropdown>
                    </template>
                  </el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <el-button :loading="downloadLoading" type="text" @click="exportExcel()">导出Excel</el-button>
            </div>
            <pagination :total="total" :page.sync="queryForm.pageNum" :limit.sync="queryForm.pageSize"
                        @pagination="pagination"/>

          </div>
        </div>
      </div>
    </div>
    <!-- 短信报送 -->
    <SendMsg :visible.sync="sendMsgDialog" :sendMsgRow="sendMsgRow" @visibleChange="visibleChange"></SendMsg>
  </div>
</template>

<script>
import moment from 'moment'
import {copyText, replaceHtml, copyAritical} from "@/utils/index"
import {resetTag, transImage} from '@/utils/index';
import SendMsg from '../fullSearch/components/sendMsg.vue';
import {
  manageAllApi,
  getWarnDataApi,
  warnReadApi,
  warnExportApi,
  warnMaterialAddApi
} from '@/api/publicOpinionWarning/index.js'
import {updateEmotion, getUrlAccessStatusApi, updateDeal, updateFollow} from "@/api/search/index";
import {getFolderList, addFolder} from '@/api/report/material.js'

export default {
  components: {SendMsg},
  data() {
    return {
      searchNum: 0,
      titleFixed: false,
      total: 0,
      downloadLoading: false,
      exportNum: null,
      sendMsgDialog: false,
      sendMsgRow: {},//短信报送对象的信息
      tableLoading: false,
      tableData: [],
      multipleSelection: {selectedRows: []},
      allTotal: 0,
      queryLoading: false,
      searchLoading: false,
      totalPage: 0,
      wordsLoading: false,
      transImage,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        warnDateStart: moment(new Date()).subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'),
        warnDateEnd: moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        emotionFlag: '',
        planId: '',
        type: [],
        sort: 3,
        selectedValues: ['all', 0, 1, 2],// 存放选中值的数组
      },
      mediaList: [],
      readList: [
        {name: '已读', value: 1},
        {name: '未读', value: 0},
      ],
      warnTypeList: [
        {name: '人工预警', value: 2},
        {name: '系统预警', value: 1},
      ],
      pushTypeList: [
        { name: '短信', value: 1 },
        { name: '邮箱', value: 2 },
        { name: '微信', value: 3 },
        { name: '系统', value: 4 },
      ],
      articleData: [
        {name: '是', value: true},
        {name: '否', value: false},
      ],
      caseData: [],
      msgData: [
        {name: '时间降序', value: 3},
        {name: '时间升序', value: 4},
      ],
      emotionData: [
        {name: '中性', value: 0},
        {name: '敏感', value: 1},
        {name: '非敏感', value: 2}
      ],
      isSelectAll: false,

      timeRange: [moment(new Date()).subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')],
      pickerOptionsStart: {
        disabledDate: (time) => {
          let endDateVal = this.queryForm.warnDateEnd;
          if (endDateVal) {
            const endDate = new Date(endDateVal);
            return time.getTime() > endDate.getTime() && time.toDateString() !== endDate.toDateString();
          }
        },
      },
      pickerOptionsEnd: {
        disabledDate: (time) => {
          let beginDateVal = this.queryForm.warnDateStart;
          if (beginDateVal) {
            const beginDate = new Date(beginDateVal);
            return time.getTime() < beginDate.getTime() && time.toDateString() !== beginDate.toDateString();
          }
        },
      },


      treeDataauto: [],
    }
  },
  watch: {
    timeRange: {
      handler(newVal, oldVal) {
        if (newVal && newVal.length == 2) {
          this.queryForm.warnDateStart = newVal[0]
          this.queryForm.warnDateEnd = newVal[1]
        }
        console.log(this.queryForm)
      },
      deep: true
    },
  },
  async created() {
    this.getPlan()
    this.getDict()
    this.getTreeData()

    if (this.$route.query.startTime) {
      this.queryForm.warnDateStart = this.$route.query.startTime
      this.queryForm.warnDateEnd = this.$route.query.endTime
    } else {
      this.queryForm.warnDateStart = moment(new Date()).subtract(3, 'day').format('YYYY-MM-DD HH:mm:ss')
      this.queryForm.warnDateEnd = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
    }
    await this.submitSearch()
  },

  mounted() {
    this.titleFixed = false;
    this.$nextTick(() => {
      document.addEventListener('scroll', this.handleScroll, true);
    })
    document.addEventListener("mouseup", (e) => {
      let treeDom = this.$refs.addInput
      if (treeDom) {
        if (!treeDom.contains(e.target)) {
          this.slideFlag = false;
        }
      }
    });
  },
  beforeDestroy() {
    document.removeEventListener('mouseup', this.showSlide);
    document.removeEventListener('scroll', this.handleScroll, true);
  },
  methods: {
    // 获取事件
    async getPlan() {
      // const res = await getPlanType('')
      const res = await manageAllApi()
      this.caseData = res.data
      // this.queryForm.planId = this.caseData.length > 0 ? this.caseData[0].planId : undefined
    },

    getDict() {
      // 来源类型
      this.getDicts('sys_media_type').then(res => {
        this.mediaList = res.data
      })
    },

    selectAll() {
      // 用于点击全选时，判断是该全选还是清除选项
      if (this.queryForm.selectedValues.length < this.emotionData.length) {
        this.queryForm.selectedValues = this.emotionData.map(item => item.value)
        this.queryForm.selectedValues.unshift('all')
      } else {
        this.queryForm.selectedValues = []
      }
    },
    // 移除select中的标签
    removeTag(val) {
      if (val === 'all') this.queryForm.selectedValues = []
    },
    handleSelectChange(val) {
      // val数组不包含'全部'项 且 其他项全部选中
      if (!val.includes('all') && val.length === this.emotionData.length) {
        this.queryForm.selectedValues = val
        this.queryForm.selectedValues.unshift('all')
      }
      // val数组包含'全部'项 且 其他项有未选中的
      if (val.includes('all') && val.length === this.emotionData.length) {
        this.queryForm.selectedValues = val
        this.queryForm.selectedValues.splice(0, 1)
      }
    },
    //滚动监听，头部固定
    handleScroll(val) {
      // if (this.tableData.length > 0) {
      //     this.$nextTick(() => {
      //         const tableFixed = this.$refs.data_table
      //         let offsetTop = tableFixed.getBoundingClientRect().top;
      //         this.titleFixed = offsetTop < 100;
      //         if (this.titleFixed) {
      //             setTimeout(() => {
      //                 const dom = document.querySelector('.is_data_fixed')
      //                 const tableDom = document.querySelector('.el-table__header-wrapper')
      //                 if (dom) {
      //                     if (tableFixed.offsetWidth - dom.offsetWidth != 20 || val) {
      //                         dom.style.width = `${tableFixed.offsetWidth - 40}px`
      //                         tableDom.style.width = `${tableFixed.offsetWidth - 40}px`
      //                     }
      //                 }
      //             }, 200);

      //         }
      //     })
      // } else {
      //     this.titleFixed = false;
      // }

      if (this.tableData.length > 0) {
        const tabFixed = this.$refs.data_table
        let offsetTop = tabFixed.getBoundingClientRect().top;

        this.titleFixed = offsetTop < document.querySelector('.fixed-header').offsetHeight;

        const marginDom = document.querySelector('.dataTable')

        if (this.titleFixed) {
          // this.$nextTick(() => {
          const dom = document.querySelector('.is_data_fixed')
          const tableDom = document.querySelector('.el-table__header-wrapper')
          const dataTotDom = document.querySelector('.dataTot')
          setTimeout(() => {
            if (dom) {
              if (tabFixed.offsetWidth - dom.offsetWidth != 20 || val) {
                dom.style.width = `${tabFixed.offsetWidth - 40}px`
                tableDom.style.width = `${tabFixed.offsetWidth - 40}px`
              }
            }
          }, 10);
          if (marginDom && dataTotDom) {
            marginDom.style.marginTop = `${tableDom.offsetHeight + dataTotDom.offsetHeight}px`
          }
          // })
        } else {
          marginDom.style.marginTop = 0
        }
      } else {
        this.titleFixed = false
      }
    },
    // 分页查询
    pagination(page) {
      this.queryForm.pageNum = page.page
      this.queryForm.pageSize = page.limit
      this.submitSearch()
      document.querySelector('.app-main').scrollTop = 430
    },
    // 列表导出
    exportExcel(id) {
      if (!this.queryForm.warnDateStart || !this.queryForm.warnDateEnd) {
        this.$message.error('请输入完整的时间范围')
        return
      }

      let params = JSON.parse(JSON.stringify(this.queryForm))
      params.type = params.type.join(',')


      // if (this.queryForm.selectedValues.includes('all')) {
      //     params.emotionFlag = '';
      // } else {
      //     params.emotionFlag = this.queryForm.selectedValues.join(',');
      // }
      delete this.queryForm.selectedValues

      const req = {...params}
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message.warning('请选择导出条数或导出项')
        return
      }
      this.downloadLoading = true
      this.$confirm('是否确认导出所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return warnExportApi(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('导出成功')
        this.download(response.msg);
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    //导出条数变动后的table选中项改变
    exportNumChange(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },


    // 获取素材树
    async getTreeData() {
      let resauto = await getFolderList()
      this.treeDataauto = resauto.data
    },
    // 批量导入至素材
    importToMaterials(item) {
      if (!this.queryForm.warnDateStart || !this.queryForm.warnDateEnd) {
        this.$message.error('请输入完整的时间范围')
        return
      }
      let params = JSON.parse(JSON.stringify(this.queryForm))
      params.type = params.type.join(',')
      delete this.queryForm.selectedValues

      const req = {...params}
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.pageSize = parseInt(this.exportNum)
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message.warning('请选择导入条数或导入项')
        return
      }

      //设置素材库收藏夹信息
      req.folderId = item.id

      this.downloadLoading = true
      this.$confirm('是否确认导入至素材?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return warnMaterialAddApi(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('导入成功')
      }).catch(() => {
        this.downloadLoading = false
      })
    },
    sortChange(sort) {
      this.queryForm.sort = sort
      this.queryForm.sort = Number(sort)
      this.submitSearch()
    },
    //处置||移除
    async markDisposition(row) {
      let val = row.deal ? 0 : 1;
      let res = await updateDeal({md5: row.md5, deal: val, indexId: row?.id, createTime: row?.publishTime})
      if (res.code == 200) {
        this.$set(row, 'deal', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'deal', row.deal);
        this.$message.error(res.msg)
      }
    },
    //重点关注||移除
    async markKeyFocus(row) {
      let val = row.follow ? 0 : 1;
      let res = await updateFollow({md5: row.md5, follow: val, indexId: row?.id, createTime: row?.publishTime})
      if (res.code == 200) {
        this.$set(row, 'follow', val);
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'follow', row.follow);
        this.$message.error(res.msg)
      }
    },
    // 复制文章
    copyAritical(row, tips) {
      copyAritical(row)
    },
    // 复制
    copyText(content, tips) {
      copyText(content, tips)
    },
    // 跳转详情页
    goOrigin(url) {
      window.open(url, '_blank')
    },
    //开启发送短信弹窗
    openSendMsg(row) {
      this.sendMsgDialog = true
      this.sendMsgRow = row
      this.sendMsgRow.keyWord1 = ''
      this.sendMsgRow.planId = ''
    },
    //同步sendMsgDialog值
    visibleChange(value) {
      this.sendMsgDialog = value
    },
    // 跳转详情页
    async goDetail(row) {
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.articleId, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
      if (row.readFlag != 1) {
        this.updateIsRead(row.id)
        await warnReadApi({id: row.id})
      }
    },
    //更新阅读状态
    updateIsRead(id) {
      const foundItem = this.tableData.find(item => item.id === id);
      if (foundItem) {
        this.$set(foundItem, 'readFlag', 1);
      }
    },
    // 切换敏感类型
    async changeSensitive(val, row) {
      let res = await updateEmotion({md5: row.md5, emotionFlag: val})
      this.$set(row, 'emotionFlag', val);
      if (res.code == 200) {
        this.$message.success('操作成功')
      } else {
        this.$set(row, 'emotionFlag', row.originFlag);
        this.$message.error(res.msg)
      }
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 查询列表
    async submitSearch(type) {
      this.searchNum++
      this.slideFlag = false
      if (type == 'search') {
        this.queryForm.pageNum = 1
      }

      if (!this.queryForm.warnDateStart || !this.queryForm.warnDateEnd) {
        this.$message.error('请输入完整的时间范围')
        return
      }

      this.exportNum = ''//翻页时将导出页数选择器重置
      try {
        this.tableLoading = true
        if (type == 'search') { //点击翻页触发该方法
          this.searchLoading = true
        } else if (type == 'query') {
          this.queryLoading = true
        }
        let params = JSON.parse(JSON.stringify(this.queryForm))
        params.type = params.type.join(',')


        // if (this.queryForm.selectedValues.includes('all')) {
        //     params.emotionFlag = '';
        // } else {
        //     params.emotionFlag = this.queryForm.selectedValues.join(',');
        // }
        delete this.queryForm.selectedValues

        console.log('params :>> ', params);
        const res = await getWarnDataApi(params)
        this.tableData = res.rows.map(item => {
          item.originFlag = item.emotionFlag;
          // 初始化 `urlAccessStatus` 属性
          this.$set(item, 'urlAccessStatus', null);
          return item;
        });
        this.tableData = res.rows
        this.tableData.map((item) => {
          item.originFlag = item.emotionFlag
        })
        this.total = Number(res.total)
        this.totalPage = Math.ceil(this.total / this.queryForm.pageSize)
        this.searchLoading = false
        this.queryLoading = false
        this.tableLoading = false
        // this.tableData = await this.enrichArrayWithDetails(this.tableData)
        await this.checkUrlAlive(this.tableData, this.searchNum)
      } catch (error) {
        console.log(error)
        this.searchLoading = false
        this.queryLoading = false
        this.tableLoading = false
      }
    },
    //校验原文是否删除
    async checkUrlAlive(data, number) {
      const urls = data.map(item => {
        return item.url
      })
      const newArray = data
      try {
        let res = await getUrlAccessStatusApi(urls)
        res.data.map((item, index) => {
          newArray[index].urlAccessStatus = item
        })
      } catch (err) {
        this.$message.error(err)
      }
      if (this.searchNum == number) {
        this.tableData = newArray;
      }
    },
    // async enrichArrayWithDetails(arrayA) {
    //     // 使用map创建一个Promise数组
    //     const promises = arrayA.map(async item => {
    //         if (this.queryForm.isOriginal != true && this.queryForm.sort != 7) {
    //             // const detail = await similarCount({md5:item.md5});
    //             return { ...item, count: 1 };
    //         } else {
    //             return { ...item, count: item.similarCount };
    //         }
    //     });
    //     const enrichedArray = await Promise.all(promises);
    //     return enrichedArray;
    // },
    goLeft() {
      if (this.totalPage >= this.queryForm.pageNum && this.queryForm.pageNum > 1) {
        this.queryForm.pageNum--
        this.submitSearch()
      }
    },
    goRight() {
      if (this.totalPage > this.queryForm.pageNum && this.totalPage > 0) {
        this.queryForm.pageNum++
        this.submitSearch()
      }
    },
    startChange(val) {
      let endDateVal = this.queryForm.warnDateEnd;
      if (endDateVal && new Date(this.queryForm.warnDateStart).getTime() > new Date(endDateVal).getTime()) {
        this.queryForm.warnDateEnd = val;
      }
    },
    endChange(val) {
      let beginDateVal = this.queryForm.warnDateStart;
      if (beginDateVal && new Date(this.queryForm.warnDateEnd).getTime() < new Date(beginDateVal).getTime()) {
        this.queryForm.warnDateStart = val;
      }
    },
    selectDictLabelArray(dictList, values) {
      if (!values || !Array.isArray(values)) {
        return '';
      }
      return dictList
           .filter(item => values.includes(item.value))
          .map(item => item.name)
          .join('  ');
    },
    // 跳转分析页面
    goAnalysis(){
      const fullPath = this.$router.resolve({
        path: '/publicOpinion/warningAnalysis',
        query: {
          planId: this.queryForm.planId,
          warnDateStart: this.queryForm.warnDateStart,
          warnDateEnd: this.queryForm.warnDateEnd
        },
      });
      window.open(fullPath.href, '_blank')
    },
  }
}
</script>

<style scoped lang="scss">
@import "./index.scss";
</style>
