<template>
  <div class="hot-event-map">
    <div ref="hotEventChart" v-loading="mapLoading" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { getAreaMapApi } from '@/api/publicOpinionWarning/index'
import chinaJson from '@/views/dataScreen/assets/china.json'

export default {
  name: 'HotEventMap',
  props: {
    chartParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      myChart: null,
      mapLoading: false,
      mapData: []
    }
  },
  watch: {
    chartParams: {
      handler() {
        this.getMapData()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    this.getMapData()
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose()
    }
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      this.myChart = echarts.init(this.$refs.hotEventChart)

      // 注册中国地图
      echarts.registerMap('china', chinaJson)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },
    
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    
    async getMapData() {
      if (!this.chartParams.startTime || !this.chartParams.endTime) {
        return
      }
      
      try {
        this.mapLoading = true
        const res = await getAreaMapApi(this.chartParams)
        
        if (res.code === 200) {
          this.mapData = res.data || []
          this.renderChart()
        } else {
          this.mapData = []
          this.renderChart()
        }
      } catch (error) {
        console.error('获取热点事件分布数据失败:', error)
        this.mapData = []
        this.renderChart()
      } finally {
        this.mapLoading = false
      }
    },
    
    renderChart() {
      if (!this.myChart) return
      
      // 处理数据格式
      const chartData = this.mapData.map(item => ({
        name: item.areaName || item.name || '',
        value: item.value || item.count || item.issueArticleNum || 0
      }))
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        visualMap: {
          min: 0,
          max: Math.max(...chartData.map(item => item.value), 100),
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true,
          inRange: {
            color: ['#fff5f5', '#ff4757']
          }
        },
        series: [
          {
            name: '热点事件',
            type: 'map',
            map: 'china',
            roam: false,
            data: chartData,
            itemStyle: {
              borderColor: '#ff6b7a',
              borderWidth: 0.5
            },
            emphasis: {
              itemStyle: {
                areaColor: '#ff4757'
              }
            }
          }
        ]
      }
      
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style scoped lang="scss">
.hot-event-map {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
