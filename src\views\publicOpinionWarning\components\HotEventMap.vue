<template>
  <div class="echarts">
    <div ref="hotEventChart" v-loading="mapLoading" element-loading-background="#FFFFFF"
      style="width: 100%; height: 100%"></div>
    <div class="mapChoose">
      <span v-for="(item, index) in parentInfo" :key="item.code">
        <span class="title" @click="chooseArea(item, index)">{{
          item.cityName == "全国" ? "中国" : item.cityName
          }}</span>
        <span v-show="index + 1 != parentInfo.length" class="icon">-</span>
      </span>
    </div>
  </div>
</template>

<script>
import axios from 'axios'
import * as echarts from 'echarts'
import { getAreaMapApi } from '@/api/publicOpinionWarning/index'

export default {
  name: 'HotEventMap',
  props: {
    chartParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      myChart: null,
      geoJson: {
        features: []
      },
      parentInfo: [],
      mapDataList: [],
      mapLoading: false,
      timer: null,
      areaInfo: {
        adcode: '100000', // 全国
        adName: '全国'
      }
    }
  },
  watch: {
    chartParams: {
      handler() {
        this.getMapData(this.parentInfo[this.parentInfo.length - 1]?.code || '100000')
      },
      deep: true
    }
  },
  mounted() {
    this.initMap(this.areaInfo)
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose()
    }
    if (this.timer) {
      clearInterval(this.timer)
    }
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initMap({ adcode, adName }) {
      this.parentInfo = [
        {
          cityName: '全国',
          code: '100000'
        }
      ]
      this.getGeoJson(adcode)
    },

    getGeoJson(adcode) {
      const that = this
      const areaCode = adcode

      axios.get(`https://oss.boryou.com/oss/geo/${areaCode}`)
        .then((res) => {
          if (res.status === 200) {
            const cleanGeoJson = res.data
            that.geoJson = cleanGeoJson
          } else {
            console.error('获取区域边界失败:', res.msg)
            that.geoJson.features = that.geoJson.features.filter(
              (item) => item.properties.adcode == adcode
            )
            if (that.geoJson.features.length === 0) {
              const parentCode = this.getParentAreaCode(adcode)
              this.getGeoJson(parentCode)
            }
          }
          that.getMapData(areaCode)
        })
        .catch((error) => {
          console.error('网络错误:', error)
        })
    },

    getParentAreaCode(areaCode) {
      areaCode = String(areaCode)
      if (typeof areaCode !== 'string' || areaCode.length !== 6) {
        return '100000'
      }
      if (areaCode.endsWith('0000')) {
        return '100000'
      }
      if (areaCode.endsWith('00')) {
        return areaCode.slice(0, 2) + '0000'
      }
      return areaCode.slice(0, 4) + '00'
    },

    async getMapData(areaCode) {
      if (!this.chartParams.startTime || !this.chartParams.endTime) {
        return
      }

      this.mapLoading = true
      const params = {
        ...this.chartParams,
        contentAreaCode: areaCode,
      }

      try {
        const res = await getAreaMapApi(params)
        this.mapLoading = false

        if (res.code == 200 && res.data && res.data.length > 0) {
          const responseData = res.data[0]
          const mapData = responseData.zList.map((item) => {
            return {
              name: item.name || '',
              cityCode: item.code || '',
              value: item.data || 0,
            }
          })

          this.mapDataList = mapData

          let pointData = responseData.zList.map((item) => {
            return {
              name: item.name,
              cityCode: item.code,
              value: [item.value[0], item.value[1], item.data],
            }
          })

          pointData = pointData.sort(function (a, b) {
            return b.value[2] - a.value[2]
          })

          this.initEcharts(mapData, pointData)
        } else {
          this.initEcharts([], [])
        }
      } catch (error) {
        console.error('获取热点事件分布数据失败:', error)
        this.mapLoading = false
        this.initEcharts([], [])
      }
    },

    initEcharts(mapData, pointData) {
      var min = pointData[pointData.length - 1]?.value[2] || 0
      var max = pointData[0]?.value[2] || 0
      if (pointData.length === 1) {
        min = 0
      }

      const _this = this
      this.myChart = echarts.init(this.$refs.hotEventChart)
      echarts.registerMap('Map', this.geoJson)

      clearInterval(_this.timer)
      this.myChart.showLoading()
      let index = -1

      // // 自动播放效果
      // _this.timer = setInterval(function() {
      //   _this.myChart.dispatchAction({
      //     type: 'hideTip',
      //     seriesIndex: 0,
      //     dataIndex: index
      //   })
      //   _this.myChart.dispatchAction({
      //     type: 'downplay',
      //     seriesIndex: 0,
      //     dataIndex: index
      //   })
      //   index++
      //   if (index > mapData.length - 1) {
      //     index = 0
      //   }
      //   _this.myChart.dispatchAction({
      //     type: 'showTip',
      //     seriesIndex: 0,
      //     dataIndex: index
      //   })
      //   if (mapData.length > 1) {
      //     _this.myChart.dispatchAction({
      //       type: 'highlight',
      //       seriesIndex: 0,
      //       dataIndex: index
      //     })
      //   }
      // }, 2000)

      // 鼠标事件
      this.myChart.on('mousemove', function (e) {
        clearInterval(_this.timer)
        _this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0
        })
        _this.myChart.dispatchAction({
          type: 'highlight',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
        _this.myChart.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
      })

      this.myChart.on('mouseout', function (e) {
        clearInterval(_this.timer)
        _this.myChart.dispatchAction({
          type: 'downplay',
          seriesIndex: 0,
          dataIndex: e.dataIndex
        })
        // 重新开始自动播放
        // _this.timer = setInterval(function() {
        //   _this.myChart.dispatchAction({
        //     type: 'hideTip',
        //     seriesIndex: 0,
        //     dataIndex: index
        //   })
        //   _this.myChart.dispatchAction({
        //     type: 'showTip',
        //     seriesIndex: 0,
        //     dataIndex: index + 1
        //   })
        //   _this.myChart.dispatchAction({
        //     type: 'downplay',
        //     seriesIndex: 0,
        //     dataIndex: index
        //   })
        //   if (mapData.length > 1) {
        //     _this.myChart.dispatchAction({
        //       type: 'highlight',
        //       seriesIndex: 0,
        //       dataIndex: index + 1
        //     })
        //   }
        //   index++
        //   if (index > mapData.length - 1) {
        //     index = -1
        //   }
        // }, 2000)
      })

      this.myChart.hideLoading()

      // 设置地图配置
      this.myChart.setOption(
        {
          tooltip: {
            trigger: 'item',
            backgroundColor: 'transparent',
            borderColor: 'transparent',
            borderWidth: 0,
            padding: 0,
            position: (point, params, dom, rect, size) => {
              return [point[0] - 70, point[1] - 72]
            },
            formatter: (p) => {
              // 如果是散点图数据，value是数组[经度, 纬度, 数值]，取第三个元素
              // 如果是地图数据，value是数值
              let numValue = 0
              if (Array.isArray(p.value)) {
                numValue = p.value[2] || 0
              } else {
                numValue = p.value || 0
              }

              const txtCon = `
                <div class='echarts_tooltip'>
                  <div class='tooltipMain'>
                    <div class='tooltipTitle'>${p.name} ${numValue}</div>
                  </div>
                </div>`
              return txtCon
            }
          },
          title: {
            show: true,
            left: 'center',
            top: '15',
            textStyle: {
              color: 'rgb(179, 239, 255)',
              fontSize: '0.16rem'
            }
          },
          toolbox: {
            feature: {
              restore: { show: false },
              dataZoom: { show: false },
              magicType: { show: false }
            },
            iconStyle: {
              borderColor: '#53D9FF'
            },
            top: 15,
            right: 35
          },
          geo: {
            map: 'Map',
            zoom: 1,
            roam: true,
            itemStyle: {
              areaColor: '#86fdff',
              shadowColor: '#86fdff',
              shadowOffsetX: 3,
              shadowOffsetY: 3,
              emphasis: {
                areaColor: '#8dd7fc'
              }
            }
          },
          visualMap: {
            show: false,
            align: 'left',
            left: '20%',
            bottom: '5%',
            calculable: true,
            seriesIndex: [0],
            inRange: {
              color: ['#105389', '#3a8abc', '#0D96F1']
            }
          },
          series: [
            {
              name: '地图',
              type: 'map',
              map: 'Map',
              selectedMode: 'none',
              roam: true,
              zoom: 1,
              data: mapData,
              geoIndex: 0,
              label: {
                show: true,
                color: '#ffffff',
                formatter: (p) => {
                  switch (p.name) {
                    case '内蒙古自治区': p.name = '内蒙古'; break
                    case '西藏自治区': p.name = '西藏'; break
                    case '新疆维吾尔自治区': p.name = '新疆'; break
                    case '宁夏回族自治区': p.name = '宁夏'; break
                    case '广西壮族自治区': p.name = '广西'; break
                    case '香港特别行政区': p.name = '香港'; break
                    case '澳门特别行政区': p.name = '澳门'; break
                    default: break
                  }
                  return p.name
                },
                emphasis: {
                  show: true,
                  color: '#ffffff'
                }
              },
              itemStyle: {
                areaColor: '#24CFF4',
                borderColor: '#53D9FF',
                borderWidth: 1.8,
                emphasis: {
                  areaColor: '#94e4ec',
                  borderWidth: 1.8,
                  shadowBlur: 25
                }
              }
            },
            {
              name: '散点',
              type: 'effectScatter',
              coordinateSystem: 'geo',
              showEffectOn: 'render',
              rippleEffect: {
                period: 15,
                scale: 4,
                brushType: 'fill'
              },
              emphasis: {
                scale: true
              },
              tooltip: {
                formatter: function (params) {
                  // 散点图专用的 tooltip 格式化，只显示名称和数值
                  const numValue = params.value[2] || 0
                  return `
                    <div class='echarts_tooltip'>
                      <div class='tooltipMain'>
                        <div class='tooltipTitle'>${params.name} ${numValue}</div>
                      </div>
                    </div>`
                }
              },
              itemStyle: {
                color: '#FF4757', // 热点事件使用红色
                shadowBlur: 10,
                shadowColor: '#333'
              },
              data: pointData,
              symbolSize: function (val) {
                if (val[2] == 0) {
                  return 0
                }
                const maxSize = 20
                const minSize = 5
                const normalizedValue = (val[2] - min) / (max - min)
                const sizeRange = maxSize - minSize
                const mappedSize = minSize + normalizedValue * sizeRange
                return mappedSize
              }
            }
          ]
        },
        true
      )

      // 设置地图事件
      this.setupMapEvents(mapData, pointData)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },

    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    },

    setupMapEvents(mapData, pointData) {
      const _this = this

      this.myChart.off('click')
      this.myChart.on('click', (params) => {
        // 直辖市判断
        const newCode = this.parentInfo[this.parentInfo.length - 1].code;
        const municipalityCodes = ['110000', '310000', '120000', '500000'];
        if (municipalityCodes.includes(newCode)) return

        const pointObj = params.data || null
        if (this.parentInfo.length < 3 && pointObj) {
          this.$emit('getCityCode', pointObj)
        }

        clearInterval(_this.timer)

        if (!params.data?.cityCode ||
          this.parentInfo[this.parentInfo.length - 1].code ==
          params.data.cityCode
        ) return

        if (this.parentInfo.length == 3) return

        if (this.parentInfo[this.parentInfo.length - 1].code == params.data.code) return

        const data = params.data
        this.parentInfo.push({
          cityName: data.name,
          code: data.cityCode
        })
        this.getGeoJson(data.cityCode)
      })
    },

    //选择切换市县
    chooseArea(val, index) {
      const pointObj = {
        cityCode: val.code,
        name: val.cityName
      }
      this.$emit('getCityCode', pointObj)

      this.parentInfo.splice(index + 1)
      this.getGeoJson(val.code)
    }
  }
}
</script>

<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
  position: relative;
  background: transparent;
  background-size: 100% 100%;

  ::v-deep div {
    box-shadow: none !important;
  }
}

.mapChoose {
  position: absolute;
  left: 0.1rem;
  top: 0.05rem;
  background: linear-gradient(to bottom, #95ddff 30%, #EFFCFE 70%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  font-size: 0.2rem;
  color: #fff;
  font-weight: bold;
  display: inline-block;
  transform: skew(-20deg, 0deg);

  .title {
    cursor: pointer;
  }
}

::v-deep .echarts_tooltip {
  font-size: 0.12rem;
  background-image: url('../../../assets/screen/tooltipPop.png');
  background-size: 100% 100%;
  max-width: 300px;

  .tooltipMain {
    padding: 0.18rem;

    .tooltipTitle {
      color: #04d9ee
    }

    .numItem {
      color: #fff;
      margin-left: 0.1rem;
    }
  }
}
</style>
