<template>
  <div class="focus-area-map">
    <div ref="focusAreaChart" v-loading="mapLoading" style="width: 100%; height: 100%"></div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { infoFocusAreaMapApi } from '@/api/publicOpinionWarning/index'
import chinaJson from '@/views/dataScreen/assets/china.json'

export default {
  name: 'FocusAreaMap',
  props: {
    chartParams: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      myChart: null,
      mapLoading: false,
      mapData: []
    }
  },
  watch: {
    chartParams: {
      handler() {
        this.getMapData()
      },
      deep: true
    }
  },
  mounted() {
    this.initChart()
    this.getMapData()
  },
  beforeDestroy() {
    if (this.myChart) {
      this.myChart.dispose()
    }
    // 移除事件监听器
    window.removeEventListener('resize', this.handleResize)
  },
  methods: {
    initChart() {
      this.myChart = echarts.init(this.$refs.focusAreaChart)

      // 注册中国地图
      echarts.registerMap('china', chinaJson)

      // 监听窗口大小变化
      window.addEventListener('resize', this.handleResize)
    },
    
    handleResize() {
      if (this.myChart) {
        this.myChart.resize()
      }
    },
    
    async getMapData() {
      if (!this.chartParams.startTime || !this.chartParams.endTime) {
        return
      }
      
      try {
        this.mapLoading = true
        const res = await infoFocusAreaMapApi(this.chartParams)
        
        if (res.code === 200) {
          this.mapData = res.data || []
          this.renderChart()
        } else {
          this.mapData = []
          this.renderChart()
        }
      } catch (error) {
        console.error('获取舆情关注度分布数据失败:', error)
        this.mapData = []
        this.renderChart()
      } finally {
        this.mapLoading = false
      }
    },
    
    renderChart() {
      if (!this.myChart) return
      
      // 处理数据格式
      const chartData = this.mapData.map(item => ({
        name: item.areaName || item.name || '',
        value: item.value || item.count || 0
      }))
      
      const option = {
        tooltip: {
          trigger: 'item',
          formatter: '{b}: {c}'
        },
        visualMap: {
          min: 0,
          max: Math.max(...chartData.map(item => item.value), 100),
          left: 'left',
          top: 'bottom',
          text: ['高', '低'],
          calculable: true,
          inRange: {
            color: ['#e0f3ff', '#006edd']
          }
        },
        series: [
          {
            name: '舆情关注度',
            type: 'map',
            map: 'china',
            roam: false,
            data: chartData,
            itemStyle: {
              borderColor: '#389BB7',
              borderWidth: 0.5
            },
            emphasis: {
              itemStyle: {
                areaColor: '#389BB7'
              }
            }
          }
        ]
      }
      
      this.myChart.setOption(option, true)
    }
  }
}
</script>

<style scoped lang="scss">
.focus-area-map {
  width: 100%;
  height: 100%;
  min-height: 300px;
}
</style>
