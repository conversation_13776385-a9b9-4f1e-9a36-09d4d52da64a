<template>
  <!-- echarts -->
  <div class="echarts" ref="map">
    <div ref="DrillMap" style="width: 100; height: 100%"></div>
  </div>
</template>

<script>
import axios from 'axios'
import * as echarts from "echarts";
import mapJson from "@/views/dataScreen/assets/china.json";
import {getAreaMap} from "@/api/search/index";

export default {
  props: {
    data: {
      type: Object,
      default: () => {},
    },
    params: {
      type: Object,
      default: () => {
      },
    },
    involves: {
      type: String,
      default: '0',
    },
    areaInfo: {
      type: Object,
      default: () => {
      },
    },
    showLoading: {
      type: Boolean,
      default: false
    },
    isToImg: {
      type: String,
      default: '',
    },
    isDown: {
      type: Boolean,
      default: false
    },
    isDataView: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      myChart: null,
      onOff: true,
      areaBarData: {},
      parentInfo: [],
      geoJson: {
        features: []
      },
      timer: null,
      mapFeatures: undefined,
      index: -1,
      // areaInfo: {
      //   adcode: '100000',
      //   adName: '全国'
      // }
    };
  },
  watch: {
    areaInfo() {
      this.$nextTick(() => {
        setTimeout(() => {
          // this.initMap(this.areaInfo) //地图初始化区域
        }, 100)
      })
    },
    data(){
        this.initChart()
    }
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
    clearInterval(this.timer);
  },
  mounted() {
    // if (mapJson !== "undefined") {
    //   echarts.registerMap("china", mapJson);
    // }

    // this.initChart();

    // this.initMap(this.areaInfo) // 地图初始化区域
     //设置开始位置（播放的位置）
            //定时播放
      this.timer = setInterval(() =>{
          //调用定时播放代码
          this.timing()
      }, 1500);

  },
  methods: {
    initMap({adcode, adName}) {
      this.parentInfo = [{
        cityName: adName,
        code: adcode
      }]
      this.getGeoJson(adcode) //地图初始化区域，和parentInfo一致
    },
    getGeoJson(adcode) {
      this.onOff = false
      const that = this

      // 使用本地区域边界数据
      const areaCode = adcode
      axios.get(`https://oss.boryou.com/oss/geo/${areaCode}`)
        .then((res) => {
          console.log('axios request success', res)
          if (res.status === 200) {
            const cleanGeoJson = res.data
            that.geoJson = cleanGeoJson
            console.log('that.geoJson', that.geoJson)
          } else {
            console.error('获取区域边界失败:', res.msg)
            // 接口获取不到geoJSON，用上一级数据过滤出点击的区县数据
            that.geoJson.features = that.geoJson.features.filter(
              (item) => item.properties.adcode == adcode
            )
            // 初始化时就为区县级，无上一级数据进行过滤，获取上一级行政区划代码
            if (that.geoJson.features.length === 0) {
              const parentCode = this.getParentAreaCode(adcode)
              this.getGeoJson(parentCode)
            }
          }
          that.onOff = true;
          that.getMapData(areaCode)
        })
        .catch((error) => {
          console.error('网络错误:', error)
        })


      // 使用高德区域边界数据
      // eslint-disable-next-line no-undef
      // AMapUI.loadUI(['geo/DistrictExplorer'], (DistrictExplorer) => {
      //   var districtExplorer = new DistrictExplorer()
      //   districtExplorer.loadAreaNode(adcode, function(error, areaNode) {
      //     if (error) {
      //       console.error(error)
      //       return
      //     }
      //     const Json = areaNode.getSubFeatures()
      //     if (Json.length > 0) {
      //       that.geoJson.features = Json
      //     } else if (Json.length === 0) {
      //       that.geoJson.features = that.geoJson.features.filter(
      //         (item) => item.properties.adcode == adcode
      //       )
      //       if (that.geoJson.features.length === 0) {
      //         that.geoJson.features = [areaNode.getParentFeature()]
      //       }
      //     }
      //     that.onOff = true;
      //     that.getMapData()
      //   })
      // })
    },
    getMapData() {
      const contentAreaCode = this.parentInfo[this.parentInfo.length - 1].code
      getAreaMap({...this.params, involves: this.involves, contentAreaCode: contentAreaCode}).then(res => {
        if (res.code == 200) {
          const mapNull = res.data[0].xList.length
          const topData = res.data[0].zList.slice(0, 3)
          const zzData = res.data[0].zList
          const xxData = res.data[0].xList
          const yyData = res.data[0].yList
          const areaBarData = {name: xxData, value: yyData, code: zzData}
          this.areaBarData = {name: xxData, value: yyData}
          const mapData = areaBarData.name.map((name, index) => {
            return {
              name: name, // 直接使用原始name数组中的值
              value: areaBarData.value[index],// 使用对应的value数组中的值
              code: areaBarData.code[index].code
            };
          });
          const areaMapData = {mapData, topData, mapNull}
          this.initChart(areaMapData)
        } else {

        }

      })
    },
     //提取自动播放的代码
    timing() {
        this.$nextTick(()=>{
          if(this.chart){
              // 取消高亮指定的数据图形
              this.chart.dispatchAction({
                  type: 'downplay',
                  seriesIndex: 0,
                  dataIndex: this.index,
              });
              // 高亮指定的数据图形
              this.chart.dispatchAction({
                  type: 'highlight',
                  seriesIndex: 0,
                  dataIndex: this.index + 1,
              });
              this.index++;
              //判断长度是否和城市的长度一样，如果一样重新播放
              if (this.index > this.mapFeatures.length) {
                  this.index = -1;
              }
          }
        })
    },
    initChart() {
      // this.$emit('getAreaData', this.areaBarData, mapNull, mapData)
      // if (this.parentInfo.length === 1) {
      //   echarts.registerMap('Map', this.geoJson); //注册
      // } else {
      //   echarts.registerMap('Map', this.geoJson); //注册
      // }
      //取消定时
      clearInterval(this.timer);
      this.chart = echarts.init(this.$refs.map);
      echarts.registerMap('China', mapJson); //注册地图
      // this.myChart = echarts.init(this.$refs.DrillMap)

      // echarts.registerMap('Map', this.geoJson) //注册
      const img2 = `image://${require("@/assets/images/mapborder.png")}`;
      // let {mapData,topData} = this.data

      const myChart = echarts.init(this.$refs.DrillMap)
      const _this = this
      myChart.off('click')
      myChart.on('click', (params) => {

        if (!this.onOff) {
          return
        }
        if (this.parentInfo.length == 3) {
          // 2市 3县
          return
        }
        if (
          this.parentInfo[this.parentInfo.length - 1].code ==
          params.data.code
        ) {
          return
        }
        const data = params.data
        this.parentInfo.push({
          cityName: data.name,
          code: data.code
        })

        this.getGeoJson(data.code)
        this.$emit('getAreaData', this.areaBarData)
      })


     
      //点击事件
      this.chart.on('click', (e)=> {
          this.chart.off('click')
          //取消定时
          clearInterval(this.timer);
          // //绘制地图
          this.initChart();
          //定时播放
          this.timer = setInterval(()=> {
              //调用定时播放代码
              this.timing()
          }, 1500);
      });
      //鼠标移出
      this.chart.on('mouseout', (e)=> {
          //取消定时
          clearInterval(this.timer);
          //取消高亮
          this.chart.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
              dataIndex: e.dataIndex,
          });
          //重新开始定时播放
          this.timer = setInterval(()=> {
              //调用定时播放代码
              this.timing()
          }, 1500);
      });
      //鼠标移入
      this.chart.on('mousemove', (e)=> {
          //取消高亮
          this.chart.dispatchAction({
              type: 'downplay',
              seriesIndex: 0,
          });
          //高亮鼠标移入的位置
          this.chart.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              dataIndex: e.dataIndex,
          });

      });
      console.log('this.data :>> ', this.data);
      const data = this.data.data
      // const data = [
      //       {
      //           "name": "广东省",
      //           "value": 1468,
      //           "polar": 0
      //       },
      //       {
      //           "name": "江苏省",
      //           "value": 1044,
      //           "polar": 0
      //       },
      //       {
      //           "name": "浙江省",
      //           "value": 886,
      //           "polar": 0
      //       },
      //       {
      //           "name": "北京市",
      //           "value": 864,
      //           "polar": 0
      //       },
      //       {
      //           "name": "安徽省",
      //           "value": 773,
      //           "polar": 0
      //       },
      //       {
      //           "name": "四川省",
      //           "value": 696,
      //           "polar": 0
      //       },
      //       {
      //           "name": "山东省",
      //           "value": 690,
      //           "polar": 0
      //       },
      //       {
      //           "name": "江西省",
      //           "value": 660,
      //           "polar": 0
      //       },
      //       {
      //           "name": "湖南省",
      //           "value": 634,
      //           "polar": 0
      //       },
      //       {
      //           "name": "福建省",
      //           "value": 566,
      //           "polar": 0
      //       },
      //       {
      //           "name": "河南省",
      //           "value": 556,
      //           "polar": 0
      //       },
      //       {
      //           "name": "陕西省",
      //           "value": 436,
      //           "polar": 0
      //       },
      //       {
      //           "name": "湖北省",
      //           "value": 402,
      //           "polar": 0
      //       },
      //       {
      //           "name": "重庆市",
      //           "value": 401,
      //           "polar": 0
      //       },
      //       {
      //           "name": "天津市",
      //           "value": 392,
      //           "polar": 0
      //       },
      //       {
      //           "name": "上海市",
      //           "value": 380,
      //           "polar": 0
      //       },
      //       {
      //           "name": "辽宁省",
      //           "value": 378,
      //           "polar": 0
      //       },
      //       {
      //           "name": "贵州省",
      //           "value": 349,
      //           "polar": 0
      //       },
      //       {
      //           "name": "广西壮族自治区",
      //           "value": 339,
      //           "polar": 0
      //       },
      //       {
      //           "name": "河北省",
      //           "value": 337,
      //           "polar": 0
      //       },
      //       {
      //           "name": "云南省",
      //           "value": 265,
      //           "polar": 0
      //       },
      //       {
      //           "name": "新疆维吾尔自治区",
      //           "value": 214,
      //           "polar": 0
      //       },
      //       {
      //           "name": "甘肃省",
      //           "value": 204,
      //           "polar": 0
      //       },
      //       {
      //           "name": "黑龙江省",
      //           "value": 200,
      //           "polar": 0
      //       },
      //       {
      //           "name": "山西省",
      //           "value": 194,
      //           "polar": 0
      //       },
      //       {
      //           "name": "内蒙古自治区",
      //           "value": 194,
      //           "polar": 0
      //       },
      //       {
      //           "name": "海南省",
      //           "value": 141,
      //           "polar": 0
      //       },
      //       {
      //           "name": "宁夏回族自治区",
      //           "value": 140,
      //           "polar": 0
      //       },
      //       {
      //           "name": "吉林省",
      //           "value": 121,
      //           "polar": 0
      //       },
      //       {
      //           "name": "台湾",
      //           "value": 91,
      //           "polar": 0
      //       },
      //       {
      //           "name": "香港特别行政区",
      //           "value": 90,
      //           "polar": 0
      //       },
      //       {
      //           "name": "西藏自治区",
      //           "value": 67,
      //           "polar": 0
      //       },
      //       {
      //           "name": "青海省",
      //           "value": 0,
      //           "polar": 0
      //       },
      //       {
      //           "name": "澳门特别行政区",
      //           "value": 0,
      //           "polar": 0
      //       }
      // ]
        var outdata = [];
        var geoCoordMap = {};
        /*获取地图数据*/
        this.mapFeatures = echarts.getMap('China').geoJson.features;
        this.mapFeatures.forEach(function (v) {
            // 地区名称
            var name = v.properties.name;
            // 地区经纬度
            geoCoordMap[name] = v.properties.center;
        });
        var convertData = function (outdata) {
            var res = [];
            for (var i = 0; i < outdata.length; i++) {
                var geoCoord = geoCoordMap[outdata[i].name];
                if (geoCoord) {
                    res.push({
                        name: outdata[i].name,
                        value: geoCoord.concat(outdata[i].value),
                    });
                }
            }
            return res;
        };

      const  option = {
          //设置背景颜色
          backgroundColor: '#040715',
          geo: {
              map: 'China',//地图为刚刚设置的China
              aspectScale: 0.75, //长宽比
              zoom: 1.2,//当前视角的缩放比例
              roam: false,//是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
              itemStyle: {//地图区域的多边形 图形样式
                  normal: {
                      areaColor: '#013C62',//地区颜色
                      shadowColor: '#182f68',//阴影颜色
                      shadowOffsetX: 0,//阴影偏移量
                      shadowOffsetY: 25,//阴影偏移量
                  },
                  emphasis: {
                      areaColor: '#2AB8FF',//地区颜色
                      label: {
                          show: false,//是否在高亮状态下显示标签
                      },
                  },
              },
          },
          series: [//数据系列
              {
                  type: 'map',//地图类型
                  //地图上文字
                  label: {
                      normal: {
                          show: true,//是否显示标签
                          textStyle: {
                              color: '#A9A9A9',
                          },
                      },
                      emphasis: {
                          textStyle: {
                              color: '#fff',
                          },
                      },
                  },
                  //地图区域的多边形 图形样式
                  itemStyle: {
                      normal: {
                          borderColor: '#2ab8ff',
                          borderWidth: 1.5,
                          areaColor: '#12235c',
                      },
                      emphasis: {
                          areaColor: '#2AB8FF',
                          borderWidth: 0,
                      },
                  },
                  zoom: 1.2,//当前视角的缩放比例
                  //是否开启鼠标缩放和平移漫游。默认不开启。如果只想要开启缩放或者平移，可以设置成 'scale' 或者 'move'。设置成 true 为都开启
                  roam: false,
                  map: 'China', //使用中国地图
                  data: data
              },
              {
                  //设置为分散点
                  type: 'scatter',
                  //series坐标系类型
                  coordinateSystem: 'geo',
                  //设置图形 'circle', 'rect', 'roundRect', 'triangle', 'diamond', 'pin', 'arrow'
                  symbol: 'pin',
                  // //标记的大小，可以设置成诸如 10 这样单一的数字，也可以用数组分开表示宽和高，例如 [20, 10] 表示标记宽为20，高为10
                  symbolSize: [40, 40],
                  //气泡字体设置
                  label: {
                      // normal: {
                          show: true,//是否显示
                          textStyle: {
                              color: '#000000',//字体颜色
                              fontSize: 12,//字体大小
                          },
                          //返回气泡数据
                          formatter(value) {
                              return value.data.value[2]
                          }
                      // }
                  },
                  itemStyle: {
                      normal: {
                          color: '#CCCC00', //标志颜色
                      }
                  },
                  //给区域赋值
                  data: convertData(data).slice(0,9),
                  showEffectOn: 'render',//配置何时显示特效。可选：'render' 绘制完成后显示特效。'emphasis' 高亮（hover）的时候显示特效。
                  rippleEffect: {//涟漪特效相关配置。
                      brushType: 'stroke'//波纹的绘制方式，可选 'stroke' 和 'fill'
                  },
                  hoverAnimation: true,//是否开启鼠标 hover 的提示动画效果。
                  zlevel: 1//所属图形的 zlevel 值
              },
          ],
      };
      this.chart.setOption(option, true);
    },
  },
};
</script>
<style lang="scss" scoped>
.echarts {
  width: 100%;
  height: 100%;
  position: relative;
}

.mapChoose {
  position: absolute;
  left: 20px;
  top: 10px;
  color: #000;

  img {
    width: 12px;
    margin-bottom: 4px;
    vertical-align: middle;
  }

  .title {
    padding: 5px;
    cursor: pointer;
  }

  .icon {
    font-family: "simsun";
    font-size: 20px;
    // margin: 0 11px;
  }
}
</style>
<style scoped lang="scss">
::v-deep {
  textarea {
    line-height: 1.8em !important;
  }
}
</style>
