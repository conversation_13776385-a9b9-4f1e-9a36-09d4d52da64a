import Cookies from 'js-cookie'

const TokenKey = 'Admin-Token-boryou'

export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(Token<PERSON><PERSON>, token, {
    sameSite: 'None',
    secure: true // 必须启用HTTPS
  })
}

export function removeToken() {
  return Cookies.remove(Token<PERSON>ey, {
    sameSite: 'None',
    secure: true // 必须启用HTTPS
  })
}
