<template>
  <div class="app-container">
    <div class="navbar">
      <el-form :model="queryParams" ref="queryParams" :inline="true">
        <el-form-item label="信息属性" prop="emotionFlag">
          <el-select v-model="queryParams.emotionFlag" placeholder="请选择信息属性" clearable size="small">
            <el-option
              v-for="(item,index) in emotionData"
              :key="index"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="方案分类" prop="typeId">
          <el-select v-model="queryParams.typeId" placeholder="请选择方案" @change="planChange" clearable size="small">
            <el-option
              v-for="item in typeOptions"
              :key="item.typeId"
              :label="item.typeName"
              :value="item.typeId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="所属方案" prop="planId">
          <el-select v-model="queryParams.planId" placeholder="请选择方案" @focus="toplan" clearable size="small">
            <el-option
              v-for="item in planList"
              :key="item.planId"
              :label="item.planName"
              :value="item.planId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="来源类型" prop="type">
          <el-select v-model="queryParams.type" placeholder="请选择类型" clearable size="small">
            <el-option
              v-for="dict in mediaList"
              :key="dict.dictValue"
              :label="dict.dictLabel"
              :value="dict.dictValue"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="信息浏览" prop="isRead">
          <el-select v-model="queryParams.isRead" placeholder="请选择浏览状态" clearable size="small">
            <el-option
              v-for="item in msgOptions"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="过滤内容" prop="text">
          <el-input
            v-model.trim="queryParams.text"
            placeholder="请输入过滤内容"
            clearable
            size="small"
            @keyup.enter.native="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="dataTable">
      <div style="margin-bottom:10px">
           <span v-show="total>0">
              <el-select size="small" v-model="exportNum" placeholder="选择过滤条数" style="width: 125px" clearable
                         @change="exportNumChange">
              <el-option label="选择当前页" value="0"/>
              <el-option label="前500条" value="500"/>
              <el-option label="前1000条" value="1000"/>
              <el-option label="前5000条" value="5000"/>
              </el-select>
              <el-button v-if="!downloadLoading" type="text" primary style="margin-left:10px" @click="handleMulFilter">批量取消过滤</el-button>
             <!-- <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg"  @click="exportExcel()" alt=""> -->
              <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;" class="el-icon-loading"></i>
          </span>
      </div>
      <el-table ref="tableRef" v-loading="tableLoading" :data="tableData" border
                style="width: 100%" :header-cell-style="{background:'#fcfcfd'}" :class="titleFixed?'result-fiexd':''"
                @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="50" align="center"></el-table-column>
        <el-table-column prop="title" label="标题" align="left" header-align="center">
          <template #default="scope">
            <div :class="scope.row.isRead==1?'tableItemTitle cover-column':'tableItemTitle'">
              <div class="tableTitle" @click="goDetail(scope.row)">
                <img class="tableItemImg" :src="transImage(scope.row.type,scope.row.host||'')" alt="无图片"/>
                <el-tooltip placement="top" effect="light" raw-content>
                  <div slot="content">
                    <div v-html="scope.row.title"></div>
                  </div>
                  <div class="tableTitleSpan">
                    <span>{{ (queryParams.pageNum - 1) * queryParams.pageSize + scope.$index + 1 }}. </span>
                    <span v-html="scope.row.title"></span>
                  </div>
                </el-tooltip>
                <el-select v-model="scope.row.emotionFlag"
                           :class="scope.row.emotionFlag==2?'emotionSelect table-nosense':scope.row.emotionFlag==1?'emotionSelect table-sense':'emotionSelect table-neutral'"
                           size="mini" placeholder="请选择" @change="(val)=>{changeSensitive(val,scope.row)}">
                  <el-option :key="2" label="非敏感" :value="2"></el-option>
                  <el-option :key="1" label="敏感" :value="1"></el-option>
                  <el-option :key="0" label="中性" :value="0"></el-option>
                </el-select>
                <p class="article-type" style="background-color: #339593" v-show="scope.row.isOriginal">原创</p>
                <p class="article-type" v-show="!scope.row.isOriginal">转载</p>
                <p class="article-type" style="background-color: #F7B8B3;color: #FA2C1C;" v-show="scope.row.warned==1">
                  流转中</p>
                <p class="article-type" style="background-color: #B2E8F3;color: #00B4D8;" v-show="scope.row.deal==1">
                  已处置</p>
                <p class="article-type" style="background-color: #F9D0AC;color: #F87500;" v-show="scope.row.follow==1">
                  已重点关注</p>
                <p class="article-type" style="background-color: #D8D8D8;color: #999999;"
                   v-if="scope.row.urlAccessStatus==0">已删除</p>
                <p class="article-type" style="background-color: #D5F8D1;color: #3EC82F;" v-else>可访问</p>
                <el-select v-hasPermi="['public:opinion:riskGrade']" v-model="scope.row.riskGrade"
                          :class="['dangerSelect',scope.row.riskGrade==2?'table-nosense':scope.row.riskGrade==1?'table-sense':'table-neutral']"
                          size="mini"
                          placeholder="暂无" @change="(val)=>changeRiskGrade(val,scope.row)">
                  <el-option :key="2" label="低风险" :value="2"></el-option>
                  <el-option :key="0" label="中风险" :value="0"></el-option>
                  <el-option :key="1" label="高风险" :value="1"></el-option>
                </el-select>
                <!-- 自定义标签组件 -->
                <CustomTagSelector v-hasPermi="['public:opinion:customTag']" :row-data="scope.row" v-model="scope.row.customTags" />
              </div>
              <div class="tableMain" v-html="scope.row.text" @click="goDetail(scope.row)"></div>
              <div class="tableFoot">
                <div class="footInfo">
                  <el-tooltip effect="light" content="评论数" placement="top">
                    <img src="@/assets/images/message.png" alt="" class="footIcon"/>
                  </el-tooltip>
                  <span>{{ scope.row.commentNum || 0 }}</span>
                </div>
                <div class="footInfo">
                  <el-tooltip effect="light" content="阅读数" placement="top">
                    <img src="@/assets/images/book.png" alt="" class="footIcon"/>
                  </el-tooltip>
                  <span>{{ scope.row.readNum || 0 }}</span>
                </div>
                <div class="footInfo">
                  <el-tooltip effect="light" content="点赞数" placement="top">
                    <img src="@/assets/images/good.png" alt="" class="footIcon"/>
                  </el-tooltip>
                  <span>{{ scope.row.likeNum || 0 }}</span>
                </div>
                <div class="footInfo">
                  <el-tooltip effect="light" content="转发数" placement="top">
                    <img src="@/assets/images/share.png" alt="" class="footIcon"/>
                  </el-tooltip>
                  <span>{{ scope.row.reprintNum || 0 }}</span>
                </div>
                <div class="footButtonGroup">
                  <div>
                    <div class="footButonItem" v-show="scope.row.hitWords">
                      <el-tooltip effect="light" content="涉及词" placement="top">
                        <img src="@/assets/images/keyword.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <el-tooltip effect="light" :content="scope.row.hitWords" placement="top">
                        <span class="keyword">{{ scope.row.hitWords || '' }}</span>
                      </el-tooltip>
                    </div>
                    <div class="footButonItem" v-show="scope.row.hitCourtNames">
                      <el-tooltip effect="light" content="涉及法院" placement="top">
                        <img src="@/assets/images/court.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <el-tooltip effect="light" :content="scope.row.hitCourtNames" placement="top">
                        <span class="keyword" style="color: #247CFF;">{{ scope.row.hitCourtNames || '' }}</span>
                      </el-tooltip>
                    </div>
                    <div class="footButonItem" v-show="scope.row.contentAreaCodeName">
                      <el-tooltip effect="light" content="精准地域" placement="top">
                        <img src="@/assets/images/areaDetail.png" alt="" class="footIcon"/>
                      </el-tooltip>
                      <el-tooltip effect="light" :content="scope.row.contentAreaCodeName" placement="top">
                        <span class="keyword" style="color: #356391;">{{ scope.row.contentAreaCodeName || '' }}</span>
                      </el-tooltip>
                    </div>
                    <div class="footButonItem" v-if="scope.row.contentMeta&&scope.row.contentMeta.length">
                      <el-tooltip effect="light" content="信息分类" placement="top">
                        <img src="@/assets/images/contentMeta.png" alt="" class="footIcon" />
                      </el-tooltip>
                      <el-tooltip effect="light" :content="scope.row.contentMeta.join(' ')" placement="top">
                        <span class="keyword" style="color: #666;">{{ scope.row.contentMeta.join(' ') }}</span>
                      </el-tooltip>
                    </div>
                  </div>

                  <div style="white-space: nowrap;">

                  </div>
                </div>
                <div>
                  作者：{{ scope.row.author || '暂无' }} <span style="margin:0px 20px;"> {{ scope.row.host }}</span>
                  {{ scope.row.url }}
                </div>
              </div>
            </div>
            <img class="read-img" v-if="scope.row.isRead==1" src="@/assets/images/read.png" alt="">
            <img class="follow-img" v-if="scope.row.follow==1" src="@/assets/images/follow.png" alt="">
          </template>
        </el-table-column>
        <el-table-column prop="planName" label="所属方案" align="center" width="100px">
        </el-table-column>
        <el-table-column prop="" label="时间" align="center" width="220px">
          <template #default="scope">
            <div>原文时间：{{ scope.row.publishTime.substring(0, 10) }}</div>
            <div>{{ scope.row.publishTime.substring(11, 19) }}</div>
            <div>过滤时间：{{ scope.row.filterTime.substring(0, 10) }}</div>
            <div>{{ scope.row.filterTime.substring(11, 19) }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="" label="操作" align="center" width="100px">
          <template slot-scope="scope">
            <el-button type="text" primary @click="handleSingleFilter(scope.row)">取消过滤</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div style="width:100%;min-height: 20px;display:flex;align-items:center;justify-content: space-between;">
        <div style="margin-top:10px;">
          <el-checkbox :disabled="total==0" v-model="checked" @change="allSelect">全选</el-checkbox>
          <!-- <el-select v-model="exportNum" size="small" placeholder="选择过滤条数" style="width: 125px" clearable
                  @change="exportNumChange">
          <el-option label="选择当前页" value="0" />
          <el-option label="前500条" value="500" />
          <el-option label="前1000条" value="1000" />
          <el-option label="前5000条" value="5000" />
          </el-select> -->
          <!-- <img src="@/assets/images/exportIcon.png" v-if="!downloadLoading" class="exportImg"  @click="exportExcel()" alt=""> -->
          <el-button v-if="!alldownloadLoading" type="text" primary style="margin-left:10px"
                     @click="handleAllMulFilter">批量取消过滤
          </el-button>
          <i v-else style="margin-left: 15px;font-size: 20px;vertical-align: middle;" class="el-icon-loading"></i>
        </div>
        <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
                    @pagination="pagination"/>

      </div>
    </div>
  </div>
</template>

<script>
import {getTypeLists} from "@/api/publicOpinionMonitor/index.js";
import {transImage,changeSensitive,changeRiskGrade} from '@/utils/index';
import {getFilterInfoPage, getPlanType, mulFilterInfo} from '@/api/system/filterMsg.js'
import {searchRead, updateEmotion} from "@/api/search/index";
import CustomTagSelector from '@/components/CustomTagSelector/index.vue';

export default {
  name: "FilterMessage",
  components: {CustomTagSelector},
  data() {
    return {
      emotionData: [
        {name: '中性', value: 0},
        {name: '敏感', value: 1},
        {name: '非敏感', value: 2}
      ],
      msgOptions: [
        {name: '已读', value: 1},
        {name: '未读', value: 0},
      ],
      typeOptions: [],
      mediaList: [],
      planList: [],
      moduleOptions: [{dictLabel: '数据概览热词云', dictValue: 0}, {dictLabel: '方案热词云', dictValue: 1}],

      // 查询参数
      queryParams: {
        emotionFlag: '',
        planId: '',
        typeId: '',
        isRead: '',
        type: '',
        text: '',
        pageNum: 1,
        pageSize: 10
      },
      total: 0,
      tableLoading: false,
      tableData: [],
      titleFixed: false,
      multipleSelection: {selectedRows: []},
      transImage,
      changeSensitive,
      changeRiskGrade,
      downloadLoading: false,
      alldownloadLoading: false,
      exportNum: null,
      checked: false,
    };
  },
  created() {
    if (this.queryParams.typeId == '') {
      this.queryParams.planId = ''
    }
    this.getList();
    this.getDict()
  },
  methods: {
    planChange(typeId) {
      this.planList = []
      const newItem = this.typeOptions.find(item => item.typeId === typeId);
      const params = {userId: newItem.userId, typeId: typeId, isOwner: true, pageSize: 0, pageNum: 1}
      getPlanType(params).then(res => {
        this.planList = res.rows
      })
    },
    toplan(val) {
      if (this.queryParams.typeId == '') {
        this.planList = []
      }
    },
    //table选中项改变
    handleSelectionChange(val) {
      this.multipleSelection.selectedRows = val
    },
    // 跳转详情页
    async goDetail(row) {
      // beforeTime.momentDay beforeTime.oneDay beforeTime.twoDay beforeTime.threeDay beforeTime.sevenDay
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.indexId, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
      if (row.isRead != 1) {
        this.updateIsRead(row.id)
        await searchRead({id: row.indexId})
      }
    },
    //更新阅读状态
    updateIsRead(id) {
      const foundItem = this.tableData.find(item => item.id === id);
      if (foundItem) {
        this.$set(foundItem, 'isRead', 1);
      }
    },
    getDict() {
      // 方案分类
      getTypeLists().then((res) => {
        this.typeOptions = res.data
      })
      // 来源类型
      this.getDicts('sys_media_type').then(res => {
        this.mediaList = res.data
      })
    },
    //导出条数变动后的table选中项改变
    exportNumChange(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    // 全选
    allSelect(val) {
      this.$refs.tableRef.clearSelection()
      if (val) {
        this.$refs.tableRef.toggleAllSelection()
      }
    },
    // 分页查询
    pagination(page) {
      this.queryParams.pageNum = page.page
      this.queryParams.pageSize = page.limit
      this.getList()
    },
    /** 查询菜单列表 */
    getList() {
      this.tableLoading = true;
      this.exportNum = null
      this.checked = false
      getFilterInfoPage(this.queryParams).then(res => {
        let tableArray = JSON.parse(JSON.stringify(res.rows))
        tableArray.map((item) => {
          item.originFlag = item.emotionFlag
          item.originalRiskGrade = item.riskGrade
          item.urlAccessStatus = null
        })
        this.tableData = tableArray
        this.total = Number(res.total)
        this.tableLoading = false;
      }).catch(err => {
        this.tableLoading = false;
      })
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryParams");
      this.handleQuery();
    },
    // 单个取消过滤
    handleSingleFilter(row) {
      let params = JSON.parse(JSON.stringify(this.queryParams))
      const newParams = {...params}
      newParams.ids = [row.id]
      this.$confirm('是否确认过滤所选的数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return mulFilterInfo(newParams);
      }).then(response => {
        this.$message.success('操作成功')
        this.getList()
      }).catch(() => {
      })
    },
    // 全选-取消过滤
    handleAllMulFilter() {
      let params = JSON.parse(JSON.stringify(this.queryParams))
      const req = {...params}
      if (this.checked) {
        req.size = 0
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message.warning('请选择过滤条数或过滤项')
        return
      }
      this.alldownloadLoading = true
      this.$confirm('是否确认过滤所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return mulFilterInfo(req);
      }).then(response => {
        this.alldownloadLoading = false
        this.$message.success('操作成功')
        this.getList()
      }).catch(() => {
        this.alldownloadLoading = false
      })
    },
    /** 批量过滤操作 */
    handleMulFilter(row) {
      let params = JSON.parse(JSON.stringify(this.queryParams))
      const req = {...params}
      if (this.exportNum) { //选项导出
        if (this.exportNum !== '0') {
          req.pageNum = 1
          req.size = parseInt(this.exportNum)
        } else {
          req.size = params.pageSize
        }
      } else if (this.multipleSelection.selectedRows.length > 0) { //勾选导出
        const ids = this.multipleSelection.selectedRows.map(item => item.id)
        req.ids = ids
      } else {
        this.$message.warning('请选择过滤条数或过滤项')
        return
      }
      this.downloadLoading = true
      this.$confirm('是否确认过滤所有类型数据项?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(function () {
        return mulFilterInfo(req);
      }).then(response => {
        this.downloadLoading = false
        this.$message.success('操作成功')
        this.getList()
      }).catch(() => {
        this.downloadLoading = false
      })

    },
  }
};
</script>
<style lang="scss" scoped>
.app-container {
  background: #F4F7FB;
  height: calc(100vh - 80px);

  .navbar {
    background: #fff;
    padding: 20px;
    margin-bottom: 20px;
  }
}

@import './index.scss';
</style>
