import { listTag } from '@/api/system/tag'

const state = {
  tagOptions: [], // 标签选项列表
  loading: false, // 加载状态
  lastLoadTime: null // 最后加载时间，用于缓存控制
}

const mutations = {
  SET_TAG_OPTIONS(state, options) {
    state.tagOptions = options
  },
  SET_LOADING(state, loading) {
    state.loading = loading
  },
  SET_LAST_LOAD_TIME(state, time) {
    state.lastLoadTime = time
  }
}

const actions = {
  async loadTagOptions({ commit, state }) {
    // 防止重复加载
    if (state.loading) {
      return
    }

    // 缓存控制：如果5分钟内已加载过，直接返回
    const now = Date.now()
    const cacheTime = 5 * 60 * 1000 // 5分钟缓存
    if (state.lastLoadTime && (now - state.lastLoadTime) < cacheTime && state.tagOptions.length > 0) {
      return
    }

    commit('SET_LOADING', true)
    
    try {
      const response = await listTag({})
      let tagOptions = []

      if (response.data) {
        if (Array.isArray(response.data)) {
          // 非分页响应
          tagOptions = response.data
        } else if (response.data.records && Array.isArray(response.data.records)) {
          // 分页响应
          tagOptions = response.data.records
        }
      }

      commit('SET_TAG_OPTIONS', tagOptions)
      commit('SET_LAST_LOAD_TIME', now)
    } catch (error) {
      console.error('加载标签选项失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 刷新标签选项（强制重新加载）
  async refreshTagOptions({ commit }) {
    commit('SET_LAST_LOAD_TIME', null) // 清除缓存时间
    commit('SET_LOADING', true)
    
    try {
      const response = await listTag({})
      let tagOptions = []

      if (response.data) {
        if (Array.isArray(response.data)) {
          tagOptions = response.data
        } else if (response.data.records && Array.isArray(response.data.records)) {
          tagOptions = response.data.records
        }
      }

      commit('SET_TAG_OPTIONS', tagOptions)
      commit('SET_LAST_LOAD_TIME', Date.now())
    } catch (error) {
      console.error('刷新标签选项失败:', error)
      throw error
    } finally {
      commit('SET_LOADING', false)
    }
  },

  // 添加新标签到本地缓存
  addTagOption({ commit, state }, newTag) {
    const updatedOptions = [...state.tagOptions, newTag]
    commit('SET_TAG_OPTIONS', updatedOptions)
  },

  // 更新标签在本地缓存
  updateTagOption({ commit, state }, updatedTag) {
    const updatedOptions = state.tagOptions.map(tag => 
      tag.id === updatedTag.id ? updatedTag : tag
    )
    commit('SET_TAG_OPTIONS', updatedOptions)
  },

  // 从本地缓存删除标签
  removeTagOption({ commit, state }, tagId) {
    const updatedOptions = state.tagOptions.filter(tag => tag.id !== tagId)
    commit('SET_TAG_OPTIONS', updatedOptions)
  }
}

const getters = {
  tagOptions: state => state.tagOptions,
  tagOptionsLoading: state => state.loading,
  tagOptionsCount: state => state.tagOptions.length,
  getTagById: state => id => state.tagOptions.find(tag => tag.id === id),
  getTagByContent: state => content => state.tagOptions.find(tag => tag.tagContent === content)
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
