<template>
  <div class="analysis-wrap">
    <div class="analysis-contain" id="myDom1">
      <div class="analysis-time-content" v-if="checkedNode&&checkedNode.historyFlag!=1||!checkedNode">
        时间范围：
        <DateRange ref="dataRangeRef" :isRepeat="queryForm.isOriginal" :ivalue="timeList.initValue"
                   :list="timeList.children"
                   @date-change="dateRanges"></DateRange>
        <el-button class="search-time" size="mini" type="primary" @click="queryData">查询</el-button>

      </div>
      <div class="wrap-text" id="statisticsText">
        在{{textData.desc.startTime}}至{{textData.desc.endTime}}，{{textData.desc.planName}}方案中涉及信息总量：{{legendData[0].value}}条，
        其中<span v-for="item in sourceData.data">{{item.name}}{{item.value}}条，</span>
        波峰值：{{peakValue}}条，非敏感占比{{textData.desc.positive}}、敏感占比{{textData.desc.negative}}、中性占比{{textData.desc.neutral}}；最大舆论场{{textData.activeText}}，活跃度{{textData.activeDegree}}；
        提及地域占比最大为{{textData.maxArea}}。
      </div>
      <div class="overview" v-loading="sourceLoading">
        <div class="overview-list overview-total">
          <div class="overview-content">
            <p class="overview-name">{{"数据总量"}}</p>
            <p class="overview-number" v-if="legendData[0]">{{legendData[0].value||0}}</p>
            <p class="overview-number" v-else>0</p>
          </div>
        </div>
        <div class="overview-list overview-item" v-for="(item,index) in legendData.slice(1)" :key="index">
          <div class="overview-content-item">
            <img class="overview-img" :src="transImage(item.type)" alt="无图片"/>
            <p class="overview-name">{{item.name=='总数'?"数据总量":item.name}}</p>
            <p class="overview-number">{{item.value||0}}</p>
          </div>
        </div>

        <!-- <div class="overview-list overview-positive">
            <div class="overview-content">
                <p class="overview-name">非敏感数据量</p>
                <p class="overview-number">{{emotionParams.positive}}</p>
            </div>
        </div>
        <div class="overview-list overview-neutral">
            <div class="overview-content">
                <p class="overview-name">中性数据量</p>
                <p class="overview-number">{{emotionParams.neutral}}</p>
            </div>
        </div>
        <div class="overview-list overview-negative">
            <div class="overview-content">
                <p class="overview-name">敏感数据量</p>
                <p class="overview-number">{{emotionParams.negative}}</p>
            </div>
        </div> -->
      </div>

      <div class="chart-wrap" ref="anchor-0">
        <div class="chart-title">
          <img src="@/assets/images/eventSummary.png" alt="">
          <div class="chart-name">事件摘要</div>
        </div>
        <div class="chart-main" style="height: fit-content">
          <!-- <img class="c_event_img"
              src="https://sghimages.shobserver.com/img/catch/2024/08/05/eef45d28-3feb-4a01-9086-9c1f55ad6596.jpg"
              onerror="javascript:this.src='/images/nopic.jpg';" id="summary_img"> -->
          <div class="c_event_detail">
            <div class="c_event_digest" v-loading="analyseSummaryLoading">
              <div v-html="analyseSummary||'暂无'" v-if="!showMore||!multText"></div>
              <div v-html="analyseMiniSummary||'暂无'" v-if="showMore&&multText"></div>
              <div v-if="showMore&&multText" class="event-expand" @click="showMore = !showMore"> <i class="el-icon-arrow-down"></i> 展开</div>
              <div v-if="!showMore&&multText" class="event-expand" @click="showMore = !showMore"><i class="el-icon-arrow-up"></i>收起</div>
            </div>
            <div class="c_event_media">
              <i class="fa fa-rss"></i>
              <span>参与此事件的重要媒体：</span>
              <div class="mediaContent" v-loading="mediaContentListLoading">
                <a v-for="(item,index) in mediaContentList" :key="index">{{item.mediaName}} {{ item.mediaNum }}</a>
              </div>
            </div>
            <div class="c_event_media">
              <i class="fa fa-globe"></i>
              <span>站点报道量排行：</span>
              <div class="siteContent" v-loading="mediaLoading">
                <a v-for="(item,index) in allMediaData.filter(item => item.name).slice(0, 5)" :key="index">{{item.name}}
                  {{ item.value }}</a>
              </div>
            </div>
          </div>
        </div>
      </div>


      <div class="chart-wrap" ref="anchor-1">
        <div class="chart-title">
          <img src="@/assets/images/firstpublish.png" alt="">
          <div class="chart-name">相关热文</div>
          <!-- <el-tooltip placement="top" effect="light">
              <div slot="content">
                  当前方案支持近一个月相关热文查询，历史方案按保存的时间区间查询。
              </div>
              <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip> -->
        </div>
        <div class="chart-main" style="height: fit-content">
          <el-table v-loading="tableLoading" :data="tableData" border style="width: 100%">
            <el-table-column prop="typeName" label="类型" align="center">
              <!-- <template slot-scope="scope">
                  <div>{{ scope.row.warnGrade==1?'一般':scope.row.warnGrade==2?'中等':scope.row.warnGrade==3?'严重':''
                      }}
                  </div>
              </template> -->
            </el-table-column>
            <el-table-column prop="host" label="来源" align="center"></el-table-column>
            <el-table-column prop="author" label="作者" align="center"></el-table-column>
            <el-table-column prop="publishTime" label="时间" align="center"></el-table-column>
            <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip>
              <template slot-scope="scope">
                <div @click="goOrigin(scope.row)"
                     style="overflow: hidden;text-overflow: ellipsis;cursor: pointer;">{{
                  replaceHtml(scope.row.title||'') }}
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
      <!-- <div class="chart-wrap"  v-show="false">
          <div class="chart-title">
              <img src="@/assets/images/eventContext.png" alt="">
              <div class="chart-name">事件脉络</div>
              <el-tooltip placement="top" effect="light">
                  <div slot="content">
                      当前方案支持近一个月事件脉络查询，历史方案按保存的时间区间查询。
                  </div>
                  <img src="@/assets/images/icon-question.png" alt="" class="name-question">
              </el-tooltip>
          </div>
          <div class="chart-main" style="height: fit-content">
              <div v-show="activities.length>0" class="eventContextList">
              <div v-for="(activity, index) in activities" :key="index" class="eventContextListItem">

                  <div class="eventContext_num">{{index+1}}</div>

                  <div class="eventContext_context">
                      <div class="context_title">
                          <span @click="goOrigin(activity)">{{replaceHtml(activity.title||'')}}</span>
                          <img v-show="activity.accountGrade==4" src="@/assets/images/isProvince.png" alt="">
                          <img v-show="activity.accountGrade==2" src="@/assets/images/isCountry.png" alt="">
                          <img v-show="activity.type==3&&activity.accountLevel==200" src="@/assets/images/daren.png"
                              alt="">
                          <img v-show="activity.type==3&&[1, 2, 3, 4, 5, 6, 7, 8, 9].includes(parseInt(activity.accountLevel))" src="@/assets/images/blueV.png"
                              alt="">
                          <img v-show="activity.type==3&&activity.accountLevel==0" src="@/assets/images/orangeV.png"
                              alt="">
                      </div>
                      <div class="context_info">
                          <span>{{ activity.publishTime }}</span>
                          <span>媒体：{{activity.host}}</span>
                          <span>相似文章数量：<span style="cursor: pointer;" @click="goSimilar(activity)">{{activity.similarCount||0}}</span></span>
                      </div>
                  </div>

              </div>
          </div>
          <el-empty v-show="activities.length==0" v-loading="timelineLoading" description="无数据"></el-empty>
          </div>
      </div> -->


      <!-- <div class="chart-wrap" ref="anchor-4">
          <div class="chart-title">
              <img src="@/assets/images/msgSource.png" alt="">
              <div class="chart-name">热门话题</div>
                  <div style="position: absolute;left: 50%;transform: translate(-50%);">
                      <el-radio-group v-model="hotTopicRadio" size="mini" @input="changeHotTopicRadio">
                          <el-radio-button v-for="(item,index) in hotTopicList" :key="index" :label="index">{{ item.typeName }}</el-radio-button>
                      </el-radio-group>
                  </div>
          </div>
          <div class="chart-main" style="height: fit-content">
              <hotTopic :data="hotTopicTableData" :hotParams="hotParams" ></hotTopic>
          </div>
      </div> -->

      <div class="chart-wrap" ref="anchor-2" v-show="hotKw">
        <div class="chart-title">
          <img src="@/assets/images/hotSearch.png" alt="">
          <div class="chart-name">相关热搜</div>
          <div style="position: absolute;left: 50%;transform: translate(-50%);">
            <el-radio-group v-model="hotSearchRadio" size="mini" @input="changeHotSearchRadio">
              <el-radio-button v-for="(item,index) in hotSearchList" :key="index" :label="index">{{ item.typeName }}
              </el-radio-button>
            </el-radio-group>
          </div>
        </div>
        <div class="chart-main" style="height: fit-content">
          <el-table
            v-loading="hotSearchLoading"
            class="hotSearchTable"
            :data="hotSearchTableData"
            style="width: 100%">
            <el-table-column prop="title" label="标题" show-overflow-tooltip>
              <template slot-scope="scope">
                <a :href="scope.row.url" style="color: #000;"
                   target="_blank">{{scope.row.title}}</a>
              </template>
            </el-table-column>
            <!-- <el-table-column prop="" label="72小时累计在榜"></el-table-column> -->
            <!-- <el-table-column prop="updateTime" label="最近一次上榜"></el-table-column> -->
            <el-table-column prop="sort" label="当前排名"></el-table-column>
            <!-- <el-table-column prop="" label="历史最高排名"></el-table-column> -->
            <el-table-column prop="indexNumStr" label="热度"></el-table-column>
            <el-table-column prop="type" label="平台"></el-table-column>
          </el-table>
        </div>
      </div>

      <div class="chart-wrap" ref="anchor-3">
        <div class="chart-title">
          <img src="@/assets/images/msgSource.png" alt="">
          <div class="chart-name">信息来源走势图</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              该时间段内分时段的多个来源类型的信息参与变化走势。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
        </div>
        <div class="chart-main">
          <lineChart v-if="type == 'second'" style="width:100%;height:100%" :data="infoLineData"
                     :legendData="legendData" chartText="" :isShow="true"
                     :toolName="'信息来源走势'" :showLoading="infoLineLoading" @goToExpendDetail="goToExpendDetail"
                     @chartRef="chartToImg" :isToImg="'line1'" :isDown="true"/>
        </div>
      </div>
      <div class="chart-wrap" ref="anchor-4">
        <div class="chart-title">
          <img src="@/assets/images/media-icon.png" alt="">
          <div class="chart-name">媒体级别分布图</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              本模块统计监测时间段内，信息在具有新闻资质的媒体中的分布情况。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
        </div>
        <div class="chart-main">
          <div class="chart-pie">
            <div class="chart-table-title">
              <div class="chart-table-title-leftBlock"></div>
              <div class="chart-table-title-text" id="mediaLevelText">
                媒体总数量：<span class="all-number">{{ mediaLevelData.mediaTotalNum }}</span><span
                style="margin-right:20px">家</span>
                媒体总发文数量：<span class="all-number">{{ mediaLevelData.mediaPublishTotal }}</span>条
              </div>
            </div>
            <barChartMedia v-if="type == 'second'" v-show="rankNull" style="width:100%;height:100%"
              :data="mediaRankData" :showLoading="rankLoading" @goToExpendDetail="goToExpendDetail"
              @chartRef="chartToImg" :isToImg="'barMedia'" :isDown="true" />
            <div v-if="rankNull ==0 && !rankLoading" class="noneData">
              <img src="@/assets/images/none.png" alt="">
              <div>暂无数据</div>
            </div>
          </div>
          <div class="chart-table">
            <div class="chart-table-title chart-media-wrap">
              <!-- <div class="chart-table-title-leftBlock"></div> -->
            <div id="mediaCountText">
              <div class="chart-table-title-leftBlock"></div>
              <div class="chart-table-title-text">媒体数量TOP20</div>
              <!-- <span class="all-number">{{ leveCount
                  }}</span>家 -->
            </div>
              <div class="media-choose">
                <el-select ref="headerSearchSelect" v-model="accountGrade" filterable clearable placeholder="请选择"
                  class="header-search-select" @change="()=>getMediaCentralData(copyTimeParams)">
                  <el-option v-for="option in accountList" :key="option.dictValue" :value="option.dictValue"
                    :label="option.dictLabel" />
                </el-select>
              </div>
            </div>
            <el-table :data="centralData" height="340" v-loading="centralLoading" style="width: 100%"
                      stripe>
              <el-table-column type="index" label="排名" width="80" align="center">
                <template slot-scope="scope">
                  <div v-html="indexMethod(scope.$index + 1)"></div>
                </template>
              </el-table-column>
              <el-table-column prop="mediaName" label="媒体网站" width="180" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div class="mediClass" @click="goToExpendDetail('媒体网站',scope.row.mediaName,scope.row.hostDown)">
                    {{scope.row.mediaName||'-'}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="mediaNum" label="信息量" align="center">
                <template slot-scope="scope">
                  <div class="mediClass">{{scope.row.mediaNum||'-'}}</div>
                </template>
              </el-table-column>
              <!-- <el-table-column prop="siteName" label="媒体账号" width="160" align="center">
                <template slot-scope="scope">
                  <div class="mediClass" @click="goToExpendDetail('媒体账号',scope.row.siteName)">
                    {{scope.row.siteName||'-'}}
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="siteNum" label="信息量" align="center">
                <template slot-scope="scope">
                  <div class="mediClass">{{scope.row.siteNum||'-'}}</div>
                </template>
              </el-table-column> -->
            </el-table>
          </div>
        </div>
      </div>
      <div class="chart-wrap" ref="anchor-5">
        <div class="chart-title">
          <img src="@/assets/images/sensitiveIcon.png" alt="">
          <div class="chart-name">敏感走势图</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              时间段内敏感信息分时段的变化走势。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
        </div>
        <div class="chart-main">
          <lineChart v-if="type == 'second'" style="width:100%;height:100%" :data="sensitiveLineData" chartText=""
                     :isShow="false"
                     :toolName="'敏感走势'" :showLoading="sensitiveLineLoading" @goToExpendDetail="goToExpendDetail"
                     @chartRef="chartToImg" :isToImg="'line2'" :isDown="true"/>
        </div>
      </div>

      <div class="chart-wrap" ref="anchor-6">
        <div class="chart-title">
          <img src="@/assets/images/sensitiveIcon.png" alt="">
          <div class="chart-name">敏感占比图</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              敏感判定由自建的情感研判模型完成。通过对内容精准切分词、中文语义分析、通过词距词序词频计算并按权重打分等方式，<br>
              根据模型训练结果的判定标准，对内容进行情感判定。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
        </div>
        <div class="chart-main">
          <div class="chart-pie">
            <pieChart v-if="type == 'second'" style="width:100%;height:100%" :show-loading="emotionLoading"
                      :toolName="'敏感占比'" :data="emotionData" @goToExpendDetail="goToExpendDetail"
                      @chartRef="chartToImg" :isToImg="'pie1'" :isDown="true"/>
          </div>
          <div class="chart-table">
            <div class="chart-table-title">
              <div class="chart-table-title-leftBlock"></div>
              <div class="chart-table-title-text">敏感信息TOP10</div>
            </div>
            <el-table :data="emotionTopData" height="340" v-loading="emotionTopLoading" style="width: 100%"
                      stripe>
              <el-table-column type="index" label="排名" width="80" align="center">
                <template slot-scope="scope">
                  <div v-html="indexMethod(scope.$index + 1)"></div>
                </template>
              </el-table-column>
              <el-table-column prop="title" label="标题" align="center" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div class="mediClass" @click="goDetail(scope.row)">{{ scope.row.title}}</div>
                </template>
              </el-table-column>
              <el-table-column prop="publishTime" label="发布时间" width="160" align="center">
              </el-table-column>
              <el-table-column prop="similarCount" label="相似文章" width="100" align="center">
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>


      <div class="chart-wrap" ref="anchor-7">
        <div class="chart-title">
          <img src="@/assets/images/msgSource.png" alt="">
          <div class="chart-name">信息来源占比</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              时间段内，微博、微信、网站等多个来源类型的信息占比情况。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
        </div>
        <div class="chart-main">
          <div class="chart-pie">
            <pieChart v-if="type == 'second'" style="width:100%;height:100%" :show-loading="sourceLoading"
                      :toolName="'信息来源占比'" :data="sourceData" :radius="['30%', '50%']" :color="colorList"
                      @goToExpendDetail="goToExpendDetail"
                      @chartRef="chartToImg" :isToImg="'pie2'" :isDown="true"/>
          </div>
          <div class="chart-table">
            <div class="chart-table-title">
              <div class="chart-table-title-leftBlock"></div>
              <div class="chart-table-title-text">来源占比信息</div>
            </div>
            <el-table :data="sourceData.data" height="340" style="width: 100%" stripe>
              <el-table-column prop="name" label="来源" align="center">
              </el-table-column>
              <el-table-column prop="value" label="信息量" align="center">
              </el-table-column>
              <el-table-column prop="percent" label="占比" align="center">
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>


      <div class="chart-wrap" ref="anchor-8">
        <div class="chart-title">
          <img src="@/assets/images/wordCloud.png" alt="">
          <div class="chart-name">关键词云</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              利用自然语义分析法，对该事件中所提及的关键词进行分词聚合，呈现出被提及频次最多的关键词；<br>
              字号越大的词组，被提及频次越多。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
        </div>
        <div class="chart-main">
          <div class="chart-pie">
            <cloudChart v-if="type == 'second'" v-show="cloudData.length" :showLoading="cloudLoading"
                        style="width:100%;height:100%"
                        ref="cloud" :data="cloudData" @filterCloud="filterLists" @goToExpendDetail="goToExpendDetail"
                        @chartRef="chartToImg" :isToImg="'cloud'" :isDown="true"></cloudChart>
            <div v-if="cloudData.length ==0 && !cloudLoading" class="noneData">
              <img src="@/assets/images/none.png" alt="">
              <div>暂无数据</div>
            </div>
          </div>
          <div class="chart-table">
            <div class="chart-table-title">
              <div class="chart-table-title-leftBlock"></div>
              <div class="chart-table-title-text">热门词频</div>
            </div>
            <el-table :data="sensitiveTableData" height="340" style="width: 100%" v-loading="cloudLoading"
                      stripe>
              <el-table-column type="index" label="排名" width="80" align="center">
                <template slot-scope="scope">
                  <div v-html="indexMethod(scope.$index + 1)"></div>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="热词" align="center" show-overflow-tooltip>
              </el-table-column>
              <el-table-column prop="value" label="提及量" align="center">
              </el-table-column>
            </el-table>
          </div>
        </div>
      </div>


      <div class="chart-wrap" ref="anchor-9">
        <div class="chart-title">
          <img src="@/assets/images/activityLevel.png" alt="">
          <div class="chart-name">媒体活跃度</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              本模块统计监测时间段内，发布信息量最多的TOP媒体平台，<br>
              此处的媒体为媒介，包括新闻、政务、自媒体等各类媒体平台。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
        </div>
        <div class="chart-main">
          <barChart v-if="type == 'second'" v-show="mediaNull" style="width:100%;height:100%" :data="mediaData"
                    chartText=""
                    :showLoading="mediaLoading" @goToExpendDetail="goToExpendDetail"
                    @chartRef="chartToImg" :isToImg="'bar'" :isDown="true"/>
          <div v-if="mediaNull ==0 && !mediaLoading" class="noneData">
            <img src="@/assets/images/none.png" alt="">
            <div>暂无数据</div>
          </div>
        </div>
      </div>
      <!-- 网民观点 -->
      <div style="height: 600px;width:800px;position: absolute;left: -9999px;top:-99999px">
        <barView style="width:100%;height:100%" :showLoading="false"
                 :data="mediaOpinion"
                 @chartRef="chartToImg" :isToImg="'bar2'" :isDown="true"></barView>
      </div>

      <div class="chart-wrap" ref="anchor-10">
        <div class="chart-title">
          <img src="@/assets/images/wordCloud.png" alt="">
          <div class="chart-name">地域分布图</div>
          <el-tooltip placement="top" effect="light">
            <div slot="content">
              时间段内，统计跟监测方案的相关的信息地域分布情况，<br>
              非微博按照备案所在地统计，微博按照用户所发微博地域统计。
            </div>
            <img src="@/assets/images/icon-question.png" alt="" class="name-question">
          </el-tooltip>
          <!-- <el-select size="small" v-model="involves" placeholder="涉及" style="margin-left: 20px; width: 125px"
                  @change="involvesChange">
                  <el-option label="全部" value="0" />
                  <el-option label="内容涉及" value="1" />
                  <el-option label="地域涉及" value="2" />
                  <el-option label="话题涉及" value="3" />
              </el-select> -->
        </div>
        <div class="chart-main" style="height: 600px" v-loading="areaLoading">
          <div class="chart-pie">
            <ChinaMapChart v-if="type == 'second'" v-show="ddNull" @getAreaData="getAreaData" :data="areaMapData"
                           :params="queryParams" :involves="involves" :areaInfo="newParams"
                           style="width:100%;height:100%"
                           @chartRef="chartToImg" :isToImg="'map'" :isDown="true"/>
            <div v-if="ddNull ==0 && !areaLoading" class="noneData">
              <img src="@/assets/images/none.png" alt="">
              <div>暂无数据</div>
            </div>
          </div>
          <div class="chart-table">
            <div class="chart-table-title">
              <div class="chart-table-title-leftBlock"></div>
              <div class="chart-table-title-text">地域分布TOP10</div>
            </div>
            <barChartArea v-if="type == 'second'" v-show="mapNull" style="width:100%;height:100%" :data="areaBarData"
                          :showLoading="areaLoading" @goToExpendDetail="goToExpendDetail"
                          @chartRef="chartToImg" :isToImg="'barArea'" :isDown="true"/>
            <div v-if="mapNull ==0 && !areaLoading" class="noneData">
              <img src="@/assets/images/none.png" alt="">
              <div>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
      <div class="chart-wrap" ref="anchor-11">
        <div class="chart-title">
          <img src="@/assets/images/cbljfx.png" alt="">
          <div class="chart-name">传播路径分析</div>
        </div>
        <div class="chart-main">
          <treeChart v-if="type == 'second'" style="width:100%;height:100%" :chartData="propagePathData"
            @chartRef="chartToImg" :isToImg="'tree'" :isDown="true"></treeChart>
        </div>
      </div>
    </div>
    <div :class="fixRight== 40 ? 'step-ul' : 'step-ul-7r' ">
      <div class="sideCatalogBg">
        <div class="step-line"></div>
        <div
          v-for="(item,index) in tabLists"
          v-show="!(item=='相关热搜'&&!hotKw)"
          :key="index"
          :class="index==tabActive ? 'tabActive' : 'ul-li'"
          @click="goAnchor('anchor-'+ index,index)"
        >
          <div class="step-icon">
            <!-- <img :src="getImageSrc(index)" alt=""> -->
            <!-- index === this.tabActive ? require('@/assets/images/tag-point.png' ): require('@/assets/images/elliptic.png') -->
            <img
              :src="index === tabActive ? require('@/assets/images/tag-point.png' ) : require('@/assets/images/elliptic.png')"
              alt="">
          </div>
          <div class="step-label">{{ item }}</div>
        </div>
      </div>


    </div>
  </div>
</template>
<script>
import moment from 'moment'
import html2canvas from 'html2canvas';
import {transImage} from '@/utils/index';
import barView from './components/barView.vue'
import lineChart from './components/lineChart.vue'
import pieChart from './components/pieChart.vue'
import cloudChart from './components/cloudChart.vue'
import barChart from './components/barChart.vue'
import barChartMedia from './components/barChartMedia.vue'
import barChartArea from './components/barChartArea.vue'
import hotTopic from './components/hotTopic.vue'
import ChinaMapChart from "@/views/dataScreen/components/ChinaMapChart.vue";
import treeChart from './components/treeChart.vue'
import {
  emotionAnalyse, mediaTypeAnalyse, emotionAnalyseTop, similarCount, wordAnalyse, getMediaActiveMap, getAreaMap, getEmtion, getType, getMedia, getMediaCentral, searchRead, hotsearchApi, analyseSummaryApi,
  mediaOpinionApi, netizenOpinionApi, propagePathApi
} from "@/api/search/index";
import {getWordsAnalyse} from '@/api/home/<USER>'
import {DeleteHotWord} from '@/api/home/<USER>'
import {replaceHtml} from '@/utils/index';
import DateRange from "@/components/DateRange/index";
import { getEventContextApi, getHotArticle } from "@/api/publicOpinionMonitor/index.js";
export default {
  components: {
    DateRange,
    barView,
    lineChart,
    pieChart,
    cloudChart,
    barChart,
    barChartArea,
    barChartMedia,
    ChinaMapChart,
    hotTopic,
    treeChart,
  },
  data() {
    return {
      showMore:false,
      multText:false,
      copyTimeParams: {},
      timeList: {
        children: [{ label: '今天', value: 0 }, { label: '24小时', value: 1 }, { label: '2天', value: 2 }, { label: '3天', value: 3 }, { label: '7天', value: 7 }],
        initValue: 0
      },
      timeParams: {},
      timeType: 'fixed',
      mediaOpinion: {},
      mediaOpinionText: '',
      mediaOpinionLoading: false,
      imgEchart: {
        lineImg1: '',
        barMediaImg: '',
        lineImg2: '',
        pieImg1: '',
        pieImg2: '',
        cloudImg: '',
        barImg: '',
        mapImg: '',
        barAreaImg: '',
      },
      tabLists: ['事件摘要', '相关热文', '相关热搜', '信息来源走势图', '媒体级别分布图', '敏感走势图', '敏感占比图', '信息来源占比', '关键词云', '媒体活跃度', '地域分布图', '传播路径分析'],
      tabActive: 0,
      offsetTopArr: [], // 缓存的offsetTop数组  
      scrollEl: undefined,
      transImage,
      legendData: [
        {
          "name": "总数",
          "value": 0
        },
        {
          "name": "客户端",
          "value": 0,
          "percent": "0.00%",
          "type": "6"
        },
        {
          "name": "微博",
          "value": 0,
          "percent": "0.00%",
          "type": "3"
        },
        {
          "name": "微信",
          "value": 0,
          "percent": "0.00%",
          "type": "5"
        },
        {
          "name": "短视频",
          "value": 0,
          "percent": "0.00%",
          "type": "11"
        },
        {
          "name": "新闻",
          "value": 0,
          "percent": "0.00%",
          "type": "1"
        },
        {
          "name": "论坛社区",
          "value": 0,
          "percent": "0.00%",
          "type": "0"
        },
        {
          "name": "政务",
          "value": 0,
          "percent": "0.00%",
          "type": "25"
        },
        {
          "name": "评论",
          "value": 0,
          "percent": "0.00%",
          "type": "26"
        },
        {
          "name": "电子报刊",
          "value": 0,
          "percent": "0.00%",
          "type": "17"
        }
      ],
      infoLineLoading: false,
      rankLoading: false,
      mediaLevelData: {},
      mediaRankData: {},
      rankNull: null,
      centralLoading: false,
      centralData: [],
      leveCount: null,
      infoLineData: {},
      sensitiveLineLoading: false,
      sensitiveLineData: {},
      accountList: [],
      accountGrade: '',
      sourceLoading: false,
      sourceData: {},
      emotionData: {},
      emotionLoading: false,
      sensitiveTableData: [],
      colorList: ['#518DEB', '#FF7800', '#E770B0', '#08C47A', '#664AE7', '#EC6764', '#98C20A'],
      cloudData: [],
      cloudLoading: false,
      emotionTopData: [],
      emotionTopLoading: false,
      queryParams: {},
      emotionParams: {negative: 0, neutral: 0, positive: 0, total: 0},
      mediaData: {},
      mediaNull: null,
      mediaLoading: false,
      involves: '0',
      areaLoading: false,
      areaMapData: {},
      areaBarData: {},
      mapNull: null,
      ddNull: null,
      textData: {
        desc: {}
      },
      peakValue: null,
      newParams: {},
      mapData: [],
      allMediaData: [],
      mediaLists: [],
      hotTopicRadio: 0,
      hotSearchRadio: 0,

      hotKw: false,
      hotTopicList: [],
      hotSearchList: [],
      hotTopicTableData: [],
      hotSearchTableData: [],
      hotSearchLoading: false,

      mediaContentList: [],
      hotParams: {},
      mediaContentListLoading: false,

      analyseSummary: '',
      analyseMiniSummary:'',
      analyseSummaryLoading: false,


      replaceHtml,
      tableLoading: false,
      tableData: [],
      timelineLoading: false,
      activities: [],

      netizenOpinionLoading: false,
      netizenOpinion: [],

      
      propagePathData: {},
    }
  },
  props: {
    queryForm: {
      default: () => {
      },
      type: Object
    },
    type: {
      default: 'first',
      type: String
    },
    checkedNode: {
      type: Object,
      default: () => {
      },
    },
    isFilter: {
      type: Boolean,
      default: false
    },
    fixRight: {
      type: Number,
      default: 40
    }
  },
  computed: {
    allLoadingsFinished() {
      return !this.emotionLoading && !this.sourceLoading && !this.analyseSummaryLoading && !this.mediaContentListLoading && !this.mediaLoading && !this.tableLoading && !this.timelineLoading && !this.hotSearchLoading && !this.cloudLoading && !this.sensitiveLineLoading && !this.infoLineLoading && !this.rankLoading && !this.centralLoading && !this.areaLoading && !this.emotionTopLoading && !this.mediaOpinionLoading && !this.netizenOpinionLoading
    }
  },
  watch: {
    type() {
      if (this.type == 'second') {
        let params = JSON.parse(JSON.stringify(this.queryForm))
        if (this.checkedNode?.id) {
          params.planId = this.checkedNode.id
        }
        // params.accountLevel = params.accountLevel.join(',')
        params.contentForm = params.contentForm.join(',')
        params.forward = params.forward.join(',')
        params.type = params.type.join(',')
        params.videoHost = params.videoHost.join(',')

        this.hotParams = params
        this.queryParams = params
        if (this.queryParams.timeIndex || this.queryParams.timeIndex == 0) {
          this.timeType = 'fixed'
          this.timeParams.startTime = undefined
          this.timeParams.endTime = undefined
          this.timeParams.timeIndex = this.queryParams.timeIndex
          this.$nextTick(() => {
            setTimeout(() => {
              this.$refs['dataRangeRef'].setParam(this.queryParams.timeIndex)
            }, 100);
          })
        } else {
          this.timeType = 'end'
          this.timeParams.startTime = this.queryParams.startTime
          this.timeParams.endTime = this.queryParams.endTime
          this.timeParams.timeIndex = undefined
          this.$nextTick(() => {
            setTimeout(() => {
              this.$refs['dataRangeRef'].setParam('999', this.queryParams.startTime, this.queryParams.endTime)
            }, 100);
          })
        }
        this.queryData()
      }
    },
    allLoadingsFinished(newValue) {
      if (newValue) {
        this.$nextTick(() => {
          this.$emit('getCanClick', true)

          let analysisData = {
            title: this.checkedNode.name,
            statisticsText: document.querySelector('#statisticsText').textContent || document.querySelector('#statisticsText').innerText,
            analyseSummary: this.analyseSummary,
            mediaContentList: this.mediaContentList,
            allMediaData: this.allMediaData.filter(item => item.name).slice(0, 5),
            tableData: this.tableData,
            hotKw: this.hotKw,
            hotSearchTableData: this.hotSearchTableData,
            infoLineData: this.infoLineData,
            legendData: this.legendData,
            mediaLevelText: document.querySelector('#mediaLevelText').textContent || document.querySelector('#mediaLevelText').innerText,
            rankNull: this.rankNull,
            mediaRankData: this.mediaRankData,
            mediaCountText: document.querySelector('#mediaCountText').textContent || document.querySelector('#mediaCountText').innerText,
            centralData: this.centralData,
            sensitiveLineData: this.sensitiveLineData,
            emotionData: this.emotionData,
            emotionTopData: this.emotionTopData,
            sourceData: this.sourceData,
            cloudData: this.cloudData,
            sensitiveTableData: this.sensitiveTableData,
            mediaNull: this.mediaNull,
            mediaData: this.mediaData,

            ddNull: this.ddNull,
            areaMapData: this.areaMapData,
            queryParams: this.queryParams,
            involves: this.involves,
            newParams: this.newParams,

            mapNull: this.mapNull,
            areaBarData: this.areaBarData,

            mediaOpinion: this.mediaOpinion,
            mediaOpinionText: this.mediaOpinionText,
            netizenOpinion: this.netizenOpinion,

            activities: this.activities,

            propagePathData: this.propagePathData,
          }
          this.$emit('getAnalysisData', analysisData)
        })
      }
    }
  },
  async mounted() {
    let res = await this.getDicts('account_level')
    this.accountList = res.data
    this.accountList.unshift({dictValue: '', dictLabel: '全部'})
    this.initializeOffsets();
    this.$nextTick(() => {
      this.scrollEl = document.querySelector('.step-ul');
    })
    window.addEventListener('scroll', this.handleScroll);
  },
  created() {
    this.getDict()
  },
  beforeDestroy() {
    window.removeEventListener('scroll', this.handleScroll);
  },

  methods: {
    // 切换时间
    async dateRanges(param) {
      this.searchNum++
      this.timeType = param.type
      switch (param.type) {
        case 'fixed':
          this.timeParams.timeIndex = param.date
          this.timeParams.startTime = undefined
          this.timeParams.endTime = undefined
          break
        case 'dymatic':
          this.timeParams.timeIndex = param.date
          this.timeParams.startTime = undefined
          this.timeParams.endTime = undefined
          break
        case 'start':
          this.timeParams.startTime = param.date
          this.timeParams.timeIndex = null
          break
        case 'end':
          this.timeParams.endTime = param.date
          this.timeParams.timeIndex = null
          break
        default:
          break
      }
    },
    getImage() {
      setTimeout(() => {
        html2canvas(document.querySelector("#myDom1")).then(canvas => {
          var dataUrl = canvas.toDataURL("image/png", 2)
          console.log('dataUrl :>> ', dataUrl);
        });
      }, 20000);
    },

    // 网民观点
    getMediaOpinion(copyTimeParams) {
      this.mediaOpinionLoading = true
      mediaOpinionApi({...this.queryParams, ...copyTimeParams}).then(res => {
        const yyData = []
        const xxData = []
        const dataArr = res.data || []
        let mediaOpinionText = ''
        dataArr.map((item) => {
          let truncatedItem = item.name.length > 20 ? item.name.slice(0, 20) + '...' : item.name;
          yyData.push(truncatedItem);
          xxData.push(item.value)
          mediaOpinionText = `${mediaOpinionText}${item.value}%的媒体关注 “${item.name}”；\n`
        })
        this.mediaOpinion = {xxData, yyData}
        this.mediaOpinionText = mediaOpinionText
        this.mediaOpinionLoading = false
      }).catch(err => {
        this.mediaOpinionLoading = false
      })
    },
    // 网民观点
    getNetizenOpinion(copyTimeParams) {
      this.netizenOpinionLoading = true
      netizenOpinionApi({...this.queryParams, ...copyTimeParams}).then(res => {
        this.netizenOpinion = res.data || []
        this.netizenOpinionLoading = false
      }).catch(err => {
        this.netizenOpinionLoading = false
      })
    },

    chartToImg(val, base) {
      // 确保 imgEchart 已被初始化
      if (!this.imgEchart) {
        this.imgEchart = {};
      }
      const imgMappings = {
        'line1': 'lineImg1',
        'barMedia': 'barMediaImg',
        'line2': 'lineImg2',
        'pie1': 'pieImg1',
        'pie2': 'pieImg2',
        'cloud': 'cloudImg',
        'bar': 'barImg',
        'map': 'mapImg',
        'barArea': 'barAreaImg',
        'bar2': 'barImg2',
        'tree': 'spreadPathImg',
      };
      //   const
      const propName = imgMappings[val]
      this.imgEchart[propName] = base;
      this.$emit('allChartimg', this.imgEchart, this.analyseSummary)
    },
    getImageSrc(index) {
      return index === this.tabActive ? require('@/assets/images/tag-point.png') : require('@/assets/images/elliptic.png');
    },
    goAnchor(selector, index) {
      // 点击后更新当前点击事件下标，显示选中样式
      setTimeout(() => {
        this.tabActive = index
      }, 300);
      // // 页面内容滚动到对应锚点位置
      this.$nextTick(() => {
        this.$refs[selector].scrollIntoView({behavior: 'smooth'})
      })
    },
    initializeOffsets() {
      this.$nextTick(() => {
        const navContents = document.querySelectorAll('.chart-wrap');
        this.offsetTopArr = []
        navContents.forEach((item, index) => {
          this.offsetTopArr.push(item.offsetTop)
        })
        this.offsetTopArr.forEach((item, index) => {
          if (!item) {//offsetTop=0,说明被隐藏了，则使用下一个元素的offsetTop-1作为当前元素的offsetTop
            if (index < this.offsetTopArr.length - 1) {
              this.offsetTopArr[index] = this.offsetTopArr[index + 1] - 1
            } else {
              this.offsetTopArr[index] = this.offsetTopArr[index + 1] + 1
            }
          }
        })
        // console.log('offsetTopArr :>> ', this.offsetTopArr,this.offsetTopArr.length);
      })
    },
    // 滚动监听器
    handleScroll(scrollTop) {
      this.$nextTick(() => {
        this.initializeOffsets()
        for (let i = 0; i < this.offsetTopArr.length; i++) {
          //  当前显示内容对应顶部tab进行显示选中效果
          if (scrollTop >= this.offsetTopArr[i] - 100) {
            this.tabActive = i;
            //                if(this.tabActive > 8){
            //     this.scrollEl.scrollTop = this.scrollEl.scrollHeight;
            // }else{
            //     this.scrollEl.scrollTo({ top: 0 });
            // }
          }
        }
      })
    },
    // 跳转详情页
    async goDetail(row) {
      console.log('row :>> ', row);
      const fullPath = this.$router.resolve({
        path: '/fullSearch/dataDetail',
        query: {id: row.id, planId: row.planId, keyWords: row.hitWords, time: row.publishTime, md5: row.md5}
      })
      window.open(fullPath.href, '_blank')
      // if (row.isRead != 1) {
      //     this.updateIsRead(row.id)
      //     await searchRead({ id: row.id })
      // }
    },
    async getDict() {
      let res = await this.getDicts('sys_media_type')
      this.mediaLists = res.data
    },
    // 详情
    async goToExpendDetail(title, name, value, type, videoHost) {
      console.log('this.checkedNode :>> ', this.checkedNode);
      console.log('title, :>> ', title, name);
      console.log('this.queryForm :>> ', this.queryForm);
      if (!name) return


      const bTitle = this.queryForm?.keyWord1 || this.checkedNode?.name;
      const newQuery = JSON.parse(JSON.stringify(this.queryForm))


      if (title == '信息来源走势' || title == '信息来源占比') {
        console.log('this.mediaLists :>> ', this.mediaLists);
        const bt = this.mediaLists.find(item => item.dictLabel === name);
        if (bt) {
          type = bt.dictValue
        } else {
          type = newQuery?.type.join(',')
        }
      } else {
        type = newQuery?.type.join(',')
      }

      // 图2
      let accountGrade;
      let author;
      let host;
      if (title == '媒体账号' || title == '媒体网站') {
        const a1 = JSON.parse(JSON.stringify(this.accountList))
        a1.shift()
        const a3 = a1.map(item => item.dictValue).join(',')
        accountGrade = this.accountGrade ? this.accountGrade : a3
        author = title == '媒体账号' ? name : ''
        host = title == '媒体网站' ? value : ''
      }
      if (title == '媒体活跃') {
        const ac = JSON.parse(JSON.stringify(this.allMediaData))
        const at = ac.find(item => item.name === name);
        if (at) {
          host = at.host
        }

      }
      if (title == '媒体级别') {
        const mc = JSON.parse(JSON.stringify(this.mediaLevelData.mediaRankData))
        const mt = mc.find(item => item.name === name);
        if (mt) {
          accountGrade = mt.type
        }
      }

      // 图3
      let emotionFlag;
      if (title == '敏感走势' || title == '敏感占比') {
        if (name == '敏感') {
          emotionFlag = '1'
        } else if (name == '中性') {
          emotionFlag = '0'
        } else if (name == '非敏感') {
          emotionFlag = '2'
        } else {
          emotionFlag = newQuery?.emotionFlag
        }
      } else {
        emotionFlag = newQuery?.emotionFlag
      }

      let hotWordCloud;
      let contentAreaCode;
      if (title == '关键词云') {
        hotWordCloud = name
      }
      if (title == '地域分布') {
        const dc = JSON.parse(JSON.stringify(this.mapData))
        const dt = dc.find(item => item.name === name);
        if (dt) {
          contentAreaCode = dt.code
        }
      }

      // 公共参数
      let planId = newQuery?.planId || ''
      videoHost = newQuery?.videoHost.join(',') || ''
      const accountAreaCode = newQuery?.accountAreaCode
      const sort = newQuery?.sort
      const isOriginal = newQuery?.isOriginal
      // const accountLevel = newQuery?.accountLevel.join(',')
      const contentForm = newQuery?.contentForm.join(',')
      const forward = newQuery?.forward.join(',')
      const noSpam = newQuery?.noSpam
      const searchPosition = newQuery?.searchPosition
      const keyWord1 = newQuery?.keyWord1

      let query = {
        startTime: this.queryForm.startTime || '',
        endTime: this.queryForm.endTime || '',
      }
      if (this.queryForm.timeIndex || this.queryForm.timeIndex == 0) {
        // let timeRange = this.updateQueryTimeRange(this.queryForm.timeIndex)
        // query.startTime = timeRange[0]
        // query.endTime = timeRange[1]
        query.startTime = this.textData.desc.startTime
        query.endTime = this.textData.desc.endTime
      }

      const planRoute = this.$router.resolve({
        path: '/home/<USER>',
        query: {
          startTime: query.startTime,
          endTime: query.endTime,
          title: bTitle,
          name: name,
          value: value,
          useCommon: 0,
          type: type,
          videoHost: videoHost,
          useArea: 0,
          planId,
          accountAreaCode,
          // accountLevel,
          contentForm,
          forward,
          noSpam,
          searchPosition,
          keyWord1,
          sort,
          isOriginal,
          accountGrade,
          author,
          host,
          emotionFlag,
          hotWordCloud,
          contentAreaCode,


        }
      })
      window.open(planRoute.href, '_blank')
    },
    async queryData() {
      if (this.timeType != 'fixed') {
        if (!this.timeParams.startTime || !this.timeParams.endTime) {
          this.$message.error('请选择正确的时间段')
          return
        }
      }
      this.copyTimeParams = JSON.parse(JSON.stringify(this.timeParams))
      this.queryEmotionAnalyse(this.copyTimeParams)
      this.queryEmotionTop(this.copyTimeParams)
      this.getCloudData(this.copyTimeParams)
      // this.getAreaData()
      this.getMediaSpreData(this.copyTimeParams)
      this.getMediaCentralData(this.copyTimeParams)
      this.getMediaContentList(this.copyTimeParams)
      this.getMediaData(this.copyTimeParams)
      this.getHotSearchData(this.copyTimeParams)
      this.getAnalyseSummary(this.copyTimeParams)

      this.getFirstRelease(this.copyTimeParams)
      // this.getEventContext(this.copyTimeParams)

      await this.queryMediaAnalyse(this.copyTimeParams)
      await this.getinfoLineData(this.copyTimeParams)
      this.getsensitiveLineData(this.copyTimeParams)
      this.getAreaResult(this.copyTimeParams)
      this.getMediaOpinion(this.copyTimeParams)
      this.getNetizenOpinion(this.copyTimeParams)

      this.getPropagePath(this.copyTimeParams)
    },
    async enrichArrayWithDetails(arrayA) {
      // 使用map创建一个Promise数组
      const promises = arrayA.map(async item => {
        const detail = await similarCount({md5: item.md5});
        return {...item, count: detail.data};
      });
      const enrichedArray = await Promise.all(promises);
      return enrichedArray;
    },

    // 过滤信息
    filterLists(item) {
      if (this.isFilter) {
        let filterParams = {word: item.name, planId: this.queryParams.planId}
        this.$confirm('此操作将过滤热词, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          DeleteHotWord(filterParams).then(res => {
            if (res.code == 200) {
              this.$message.success('操作成功')
              this.getCloudData(this.copyTimeParams)
            }
          })
        })
      }
    },
    // 媒体分布图
    async getMediaSpreData(copyTimeParams) {
      try {
        this.rankLoading = true
        let res = await getMedia({...this.queryParams, ...copyTimeParams})//替换
        // this.rankLoading = false
        this.mediaLevelData = res.data
        const yyData = []
        const xxData = []
        const dataArr = res.data.mediaRankData || []
        dataArr.map((item) => {
          yyData.push(item.name)
          xxData.push(item.count)
        })
        this.mediaRankData = {xxData, yyData}
        this.rankNull = xxData.length
      } finally {
        this.rankLoading = false
      }
    },
    // 媒体数量
    async getMediaCentralData(copyTimeParams) {
      try {
        this.centralLoading = true
        let res = await getMediaCentral({...this.queryParams, accountGrade: this.accountGrade, ...copyTimeParams})
        this.centralData = res.data.rankDatas
        this.leveCount = res.data.leveCount
      } finally {
        this.centralLoading = false
        this.getMediaSpreData(this.copyTimeParams)
      }
    },
    //信息来源走势图
    async getinfoLineData(copyTimeParams) {
      try {
        this.infoLineLoading = true
        let res = await getType({...this.queryParams, ...copyTimeParams})//替换
        this.infoLineLoading = false
        this.infoLineData = res.data
        const num = res.data.seriesList[0].data
        this.peakValue = Math.max(...num);
      } finally {
        this.infoLineLoading = false
      }
    },
    //敏感走势图
    async getsensitiveLineData(copyTimeParams) {
      try {
        this.sensitiveLineLoading = true
        let res = await getEmtion({...this.queryParams, ...copyTimeParams})//替换
        this.sensitiveLineLoading = false
        this.sensitiveLineData = res.data
      } finally {
        this.sensitiveLineLoading = false
      }
    },

    // 信息来源占比
    async queryMediaAnalyse(copyTimeParams) {
      try {
        this.sourceLoading = true
        let res = await mediaTypeAnalyse({...this.queryParams, ...copyTimeParams})
        this.sourceData = {data: res.data}
        let legendData = JSON.parse(JSON.stringify(res.data))
        const sum = legendData.reduce((accumulator, current) => accumulator + current.value, 0);
        this.legendData = [{name: '总数', value: sum}, ...legendData]
        console.log(this.sourceData, 'legendData')
      } finally {
        this.sourceLoading = false
      }
    },
    // 敏感信息占比
    async queryEmotionAnalyse(copyTimeParams) {
      try {
        this.emotionLoading = true
        let res = await emotionAnalyse({...this.queryParams, ...copyTimeParams})
        this.emotionData = {data: res.data.data}
        this.emotionParams = res.data.prarms
        this.textData.desc = res.data.desc
      } finally {
        this.emotionLoading = false
      }
    },
    // 敏感信息top10
    async queryEmotionTop(copyTimeParams) {
      try {
        this.emotionTopLoading = true
        let res = await emotionAnalyseTop({...this.queryParams, ...copyTimeParams})
        this.emotionTopData = res.data
      } finally {
        this.emotionTopLoading = false
      }
    },
    indexMethod(index) {
      if (index == 1) {
        return `<div style="color:#FF0000">${index}</div>`
      } else if (index == 2) {
        return `<div style="color:#FF7F2D">${index}</div>`
      } else if (index == 3) {
        return `<div style="color:#FFB10E">${index}</div>`
      } else {
        return index
      }
    },
    // 热词云
    async getCloudData(copyTimeParams) {
      this.cloudData = []
      this.cloudLoading = true
      wordAnalyse({...this.queryParams, ...copyTimeParams}).then(res => {
        this.cloudData = res.data || []
        let params = JSON.parse(JSON.stringify(res.data))
        this.sensitiveTableData = params.length <= 10 ? [] : params.slice(0, 10)
        this.cloudLoading = false
      }).catch(err => {
        this.cloudLoading = false
      })
    },

    async getMediaData(copyTimeParams) {
      try {
        this.mediaLoading = true
        let res = await getMediaActiveMap({...this.queryParams, ...copyTimeParams})
        this.mediaLoading = false
        this.allMediaData = res.data
        const yyData = []
        const xxData = []
        const dataArr = res.data || []
        dataArr.map((item) => {
          yyData.push(item.name)
          xxData.push(item.value)
        })
        this.mediaData = {xxData, yyData}
        this.mediaNull = xxData.length
        this.textData.activeDegree = this.mediaData.xxData[0]
        this.textData.activeText = this.mediaData.yyData[0]
        // this.mediaData = {
        //     name: ["新浪微博", "微信", "lofter", "一点资讯", "博览黄页", "搜狐号", "新浪财经"],
        //     value: [69, 38, 1, 1, 1, 1, 1]
        // }
      } finally {
        this.mediaLoading = false
      }
    },
    //地域分布图-涉及范围切换
    involvesChange() {
      // this.getAreaData()
      this.$refs['ChinaMapChartRef'].getMapData()
    },
    async getAreaResult(copyTimeParams) {
      let res = await getAreaMap({...this.queryParams, involves: this.involves, ...copyTimeParams})
      this.textData.maxArea = res.data[0].max
      this.ddNull = res.data[0].xList.length
      this.newParams = {
        adcode: res.data[0]?.code,
        adName: res.data[0]?.name || '全国'
      }
      console.log(this.newParams, 'newParams')
    },
    //地域分布图
    async getAreaData(value, long, mapData) {
      this.areaLoading = true
      if (value) {
        this.areaBarData = value
        this.mapData = mapData
      }
      this.mapNull = long
      this.areaLoading = false
      // try {
      //     this.areaLoading = true
      //     let res = await getAreaMap({ ...this.queryParams, involves: this.involves })
      //     this.areaLoading = false
      //     const xxData = res.data[0].xList
      //     const yyData= res.data[0].yList
      //     const topData = res.data[0].zList.slice(0,3)
      //     this.areaBarData = { name: xxData, value: yyData}
      //     this.textData.maxArea = res.data[0].max
      //     const mapData = this.areaBarData.name.map((name, index) => {
      //         return {
      //             name: name, // 直接使用原始name数组中的值
      //             value: this.areaBarData.value[index] // 使用对应的value数组中的值
      //         };
      //     });
      //     this.areaMapData = { mapData,topData}
      // } finally {
      //     this.areaLoading = false
      // }
    },

    async getHotSearchData(copyTimeParams) {
      if (!this.queryParams.planId || this.hotKw == false) {//全文搜索没有planId,hotKw为false则不请求
        return this.hotKw = false
      }
      this.hotSearchLoading = true
      hotsearchApi({...this.queryParams, ...copyTimeParams}).then(res => {
        this.hotSearchLoading = false
        const {hotWords, topic, hotKw} = res.data
        this.hotTopicList = topic
        this.hotSearchList = hotWords
        this.hotKw = hotKw
        this.hotTopicTableData = this.hotTopicList[this.hotTopicRadio]?.data || []
        this.hotSearchTableData = this.hotSearchList[this.hotSearchRadio]?.data || []

      }).catch(err => {
        this.hotSearchLoading = false
      })
    },

    changeHotTopicRadio(index) {
      this.hotTopicTableData = this.hotTopicList[index]?.data || []
    },
    changeHotSearchRadio(index) {
      this.hotSearchTableData = this.hotSearchList[index]?.data || []
    },
    countSubstringRegex(str) {
        const target = '<br/>';
        const regex = new RegExp(target, 'g'); // 创建全局匹配的正则表达式
        const matches = str.match(regex);
        console.log(matches,'matches');
        
        return matches? matches.length : 0;
    },
    cutBeforeTenthRegex(str) {
        let index = -1;
        let count = 0;
        let target = '<br/>';
        while (count < 10) {
            index = str.indexOf(target, index + 1);
            if (index === -1) {
                break;
            }
            count++;
        }
        if (index === -1) {
            return str;
        } else {
            return str.slice(0, index);
        }
    },
    getAnalyseSummary(copyTimeParams) {
      this.analyseSummaryLoading = true
      analyseSummaryApi({...this.queryParams, ...copyTimeParams}).then(res => {
        this.analyseSummary = res.data
        this.multText = this.countSubstringRegex(res.data) > 10?true:this.countSubstringRegex(res.data)== 0&&res.data.length>600?1:false
        this.showMore =  this.countSubstringRegex(res.data) > 10?true:this.countSubstringRegex(res.data)== 0&&res.data.length>600?1:false
        this.analyseMiniSummary =  this.multText==true?this.cutBeforeTenthRegex(res.data):this.multText==1?res.data.slice(0,600):res.data
        this.analyseSummaryLoading = false
        // this.$emit('getCanClick',true)
      }).catch(err => {
        this.analyseSummaryLoading = false
      })
    },

    // 参与此事件的重要媒体
    async getMediaContentList(copyTimeParams) {
      try {
        this.mediaContentListLoading = true
        let res = await getMediaCentral({...this.queryParams, accountGrade: '2,4,6,8', ...copyTimeParams})
        this.mediaContentList = res.data.rankDatas.filter(item => item.mediaName).slice(0, 5)
        this.mediaContentListLoading = false
      } finally {
        this.mediaContentListLoading = false
      }
    },


    //获取相关热文
    getFirstRelease(copyTimeParams) {
      let params = this.hotParams
      // if(this.checkedNode?.id){
      //     params.planId=this.checkedNode.id
      // }else{
      //     params=this.queryForm
      // }

      this.tableLoading = true
      getHotArticle({...params, ...copyTimeParams}).then(res => {
        if (res.code == 200) {
          this.tableData = res.data
          this.tableLoading = false
        }
      }).catch(err => {
        this.tableLoading = false
      })
    },
    //获取事件脉络
    getEventContext(copyTimeParams) {
      let params = {
        ...this.hotParams,
        // planId: this.checkedNode.id,
        pageSize: 10,
        // pageNum: this.pageNum,
        pageNum: 1,
        ...copyTimeParams
      }
      // if(this.checkedNode?.id){
      //     params.planId=this.checkedNode.id
      // }else{
      //     params=this.queryForm
      // }
      this.timelineLoading = true
      getEventContextApi(params).then(res => {
        if (res.code == 200) {
          this.activities = res.rows
          this.total = res.total
          this.timelineLoading = false
        }
      }).catch(err => {
        this.timelineLoading = false
      })
    },

    // 获取传播路径
    getPropagePath(copyTimeParams){
      let params = {
        ...this.queryParams,
        ...copyTimeParams
      }
      propagePathApi(params).then(res => {
        if (res.code == 200) {
          this.propagePathData = res.data
        }
      }).catch(err => {
      })
      
    },
    // 查看原文
    goOrigin(item) {
      window.open(item.url, '_blank')
    },
    goSimilar(row) {
      if (row.similarCount <= 1) {
        return
      }
      let query = {
        startTime: this.queryForm.startTime || '',
        endTime: this.queryForm.endTime || '',
        id: row.id,
        count: row.similarCount,
        time: row.publishTime
      }
      if (this.checkedNode?.id) {
        query.planId = this.checkedNode.id
      } else {
        query.keyWord1 = this.queryForm.keyWord1
      }

      if (this.queryForm.timeIndex || this.queryForm.timeIndex == 0) {
        let timeRange = this.updateQueryTimeRange(this.queryForm.timeIndex)
        query.startTime = timeRange[0]
        query.endTime = timeRange[1]
      }
      if (this.checkedNode?.historyFlag && this.checkedNode.historyFlag == '0') {//当前方案只查一个月
        console.log('当前方案只查一个月')
        query.startTime = moment(new Date()).subtract(1, 'months').format('YYYY-MM-DD 00:00:00')
        query.endTime = moment(new Date()).format('YYYY-MM-DD HH:mm:ss')
      }
      console.log('query', query)
      const planRoute = this.$router.resolve({
        path: '/fullSearch/similarArticles',
        query
      })
      window.open(planRoute.href, '_blank')
    },
    updateQueryTimeRange(timeIndex) {
      if (timeIndex == 0) {
        return [moment(new Date()).format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      } else {
        return [moment(new Date()).subtract(timeIndex, 'days').format('YYYY-MM-DD 00:00:00'), moment(new Date()).format('YYYY-MM-DD HH:mm:ss')]
      }
    },

  }
}
</script>
<style scoped lang="scss">
.event-expand{
    display: inline-block;
    cursor: pointer;
    color:#1989fe;
}
.analysis-time-content {
  padding: 2px 0 0 20px;
  display: flex;
  align-items: center;

  .search-time {
    margin-left: 10px;
  }
}

@import "./analysis.scss";
</style>
